# UniBabble 快速配置指南

## 立即启动项目

### 第一步：配置 Google OAuth 2.0

1. **创建 Google Cloud 项目**
   - 访问 [Google Cloud Console](https://console.cloud.google.com/)
   - 创建新项目或选择现有项目
   - 启用 "Google+ API" 或 "Google Identity" 服务

2. **创建 OAuth 2.0 凭据**
   - 在左侧菜单选择 "APIs & Services" > "Credentials"
   - 点击 "CREATE CREDENTIALS" > "OAuth client ID"
   - 应用类型选择 "Web application"
   - 授权的 JavaScript 源设置为：
     ```
     http://localhost:5173 (开发环境)
     https://yourdomain.com (生产环境)
     ```
   - 获取 Client ID 和 Client Secret

3. **配置环境变量**

   **客户端配置** (packages/client/.env.dev):
   ```env
   VITE_AUTH_ENABLED=true
   VITE_APP_GOOGLE_CLIENT_ID=your_actual_client_id.apps.googleusercontent.com
   ```

   **服务端配置** (packages/server/.env.dev):
   ```env
   SERVER_AUTH_ENABLE=true
   SERVER_GOOGLE_CLIENT_ID=your_actual_client_id.apps.googleusercontent.com
   SERVER_GOOGLE_CLIENT_SECRET=your_actual_client_secret
   ```

### 第二步：配置翻译服务 (可选)

如需自动翻译功能，配置 DeepL API：

```env
# 在 packages/server/.env.dev 中添加
SERVER_TRANSLATION_DEEPL_ENABLE=true
SERVER_TRANSLATION_DEEPL_API_KEY=your_actual_deepl_api_key
```

获取 DeepL API Key: https://www.deepl.com/pro-api

### 第三步：启动服务

```bash
# 安装依赖
pnpm install

# 构建共享模块
pnpm build:shared

# 启动后端服务 (在一个终端)
pnpm dev:server

# 启动前端服务 (在另一个终端)
pnpm dev:client
```

### 第四步：测试核心功能

1. 访问 http://localhost:5173
2. 点击 "Login with Google" 使用 Google 账号登录
3. 选择语言并开启 "Random" 模式
4. 点击 "Start" 开始随机匹配
5. 等待匹配成功后开始聊天

## 常见问题

### Q: Google 登录失败？
- 检查 Client ID 是否正确配置
- 确认域名在 Google Console 中已授权
- 检查环境变量是否正确加载

### Q: 随机匹配找不到用户？
- 确保至少有 2 个用户都开启了随机模式
- 检查服务端日志确认用户已进入随机池

### Q: 无法发送消息？
- 确认已成功登录且加入房间
- 检查 WebSocket 连接状态
- 查看浏览器控制台和服务端日志

## 安全提醒

- 不要在代码仓库中提交真实的 API 密钥
- 生产环境务必使用 HTTPS
- 定期更新 OAuth 密钥和 API 密钥

## 下一步

- 配置生产环境域名和证书
- 添加数据库存储消息历史
- 实现用户头像和个人资料
- 添加更多语言支持

---

如有问题，请检查 `docs/project-analysis-report.md` 中的详细分析报告。