# 应用配置
NODE_ENV=development
PORT=3000
HOST=localhost

# 前端配置
VITE_APP_TITLE=UniBabble
VITE_WS_URL=ws://localhost:3000/ws
VITE_API_URL=http://localhost:3000/api

# Google OAuth 配置
# 获取方式: https://console.cloud.google.com/
VITE_APP_GOOGLE_CLIENT_ID=your_client_id.apps.googleusercontent.com
VITE_AUTH_ENABLED=true
SERVER_GOOGLE_CLIENT_ID=your_client_id.apps.googleusercontent.com
SERVER_GOOGLE_CLIENT_SECRET=your_client_secret
SERVER_AUTH_ENABLE=true

# 翻译服务配置
# DeepL API (推荐)
SERVER_TRANSLATION_DEEPL_ENABLE=true
SERVER_TRANSLATION_DEEPL_API_KEY=your_deepl_api_key

# Mistral API (可选)
SERVER_TRANSLATION_MISTRAL_ENABLE=false
SERVER_TRANSLATION_MISTRAL_API_KEY=your_mistral_api_key


# WebSocket配置
WS_PATH=/ws
WS_PING_INTERVAL=30000
WS_PING_TIMEOUT=5000
WS_MAX_HISTORY_SIZE=100
WS_MAX_MESSAGE_LENGTH=5000
WS_MESSAGE_RATE_LIMIT=60
WS_TRANSLATION_TIMEOUT=10000
WS_MESSAGE_RETRY_ATTEMPTS=3
WS_MESSAGE_RETRY_DELAY=1000

# 日志配置
LOG_LEVEL=debug

# CORS 配置
CORS_ORIGIN=http://localhost:5173