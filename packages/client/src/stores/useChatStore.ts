import { catchErr, doNothing } from '@/utils';
import type {
  SystemPayload,
  ErrorPayload,
  Message,
  Room,
  User,
  SendMessagePayload,
  TextMessageWithState,
  JoinRoomPayload,
  LeaveRoomPayload,
  ClientSocket,
  RoomUpdatedPayload,
  MessageDeliveredPayload,
  HistorySharePayload,
  LanguageCode,
  ServerEmitEvents,
  LanguageSource,
  KickUserPayload,
  UserKickedPayload,
  UserJoinedPayload,
  UserLeftPayload,
  ServerStatusPayload,
  ClientPayload,
  AckPayload,
  InvitePayload,
  ServerMatchRandomPayload,
} from '@unibabble/shared';

import {
  logger,
  ErrorCode,
  EMessageType,
  EMessageState,
  WebSocketEvents,
  getTimestamp,
  genUserMsgId,
  createErrorMessage,
  ErrSource,
  createCliErr,
  cliErr__errPayload,
  unknown__cliErr,
  getLanguageName,
  FixedLengthArray,
  SystemMessageTemplate,
  createSystemMessage,
  EChatModeType,
  EServerAckType,
} from '@unibabble/shared';
import { cliConfig } from '@/config';
import { apiLeaveRoom, apiIsInRoom, translate, UserService, GoogleAuth } from '@/services';
import { CliSocket } from '@/utils'
import type { InviteDesc, InviteStatusDesc } from '@/types';


const MODULE_NAME = 'client:useChatStore';

const useChatStore = defineStore('chat', () => {
  // 核心状态
  const isConnected = ref(false);
  const isConnecting = ref(false);
  const isInRoom = ref(false);
  const needLeave = ref(true);

  const currentUser = ref<User | null>(null);
  const currentRoom = ref<Room | null>(null);
  const messages = ref<Map<string, Message>>(new Map());
  const error = ref<ErrorPayload | null>(null);
  
  const inviteStatus = ref<InviteStatusDesc | null>(null)
  const inviteInfo = ref<InviteDesc | null>(null)
  const inviteAck = ref<EServerAckType | null>(null)
  const randomRoomId = ref<number | null>(null)

  const latencies = new FixedLengthArray<number>(1000);
  const avgLatency = computed(() => latencies.len ? latencies.reduce((acc, cur) => acc + cur, 0) / latencies.len : 0)
  const maxLatency = computed(() => latencies.len ? Math.max(...latencies) : 0)
  const minLatency = computed(() => latencies.len ? Math.min(...latencies) : 0)

  const enableTranslate = ref(false)
  const selectedTargetLangCode = ref<LanguageCode>('EN-US')
  const selectedSourceLangCode = ref<LanguageSource>('EN')

  const chatMode = ref<EChatModeType>(EChatModeType.NORMAL)
  const population = ref(0)
  const randomPopulation = ref(0)


  let isListenersSetup = false;

  const cliSocket = CliSocket.getSocket()
  // 用户级别全局唯一, 单次访问来说
  // TODO: 刷新页面识别登录状态; 长期登录超时失效
  let currentUserToken = ''

  const handleGoogleLogin = (credential: string) => {
    try {
      currentUser.value = GoogleAuth.login(credential)
    } catch (err) {
      error.value = err as ErrorPayload
    }
  }

  const handleLogout = async () => {
    try {
      // 清除 Google 登录状态
      GoogleAuth.cleanToken()
      currentUser.value = null

      cleanConnectionAndApiLeaveRoom(true, false)

      // 如果需要，可以在这里添加其他清理逻辑
      // 例如：清除聊天记录、断开 WebSocket 连接等
      // messages.value.clear()
      // isConnected.value = false

      // 如果在房间中，需要离开房间
      if (currentRoom.value) {
        await leaveRoom()
      }

      return Promise.resolve()
    } catch (err) {
      console.error('Error during logout:', err)
      return Promise.reject(err)
    }
  }

  const createUser = () => {
    currentUser.value = {
      id: UserService.getUserId(),
      name: UserService.getUsername(),
      lang: UserService.getDefaultUserLang(),
      email: UserService.getUserEmail()
    }
  }

  function cleanConnection() {
    isConnected.value = false;
    cliSocket.disconnect()
    isListenersSetup = false
  }

  /**
   *  clear connection and leave room when ws connect is fail/error or room update
   * */
  async function cleanConnectionAndApiLeaveRoom(clearClient: boolean, needApi = true, isCleanConn = true) {
    isCleanConn && cleanConnection()
    needApi && await apiLeaveRoom()
    if (clearClient) {
      currentRoom.value = null;
      enableTranslate.value = false;
      messages.value.clear();
      isInRoom.value = false;
      error.value = null;
      
      randomRoomId.value = null;
      inviteStatus.value = null
      inviteInfo.value = null
      inviteAck.value = null
    }
  }

  function setupSocketListeners(socket: ClientSocket) {

    const setupStandardListeners = () => {
      // Socket.IO 客户端在发现连接断开时会自动触发 disconnect 事件
      // required
      socket.on(WebSocketEvents.STANDARD.CLIENT.DISCONNECT, (reason) => {
        switch (reason) {
          case ErrorCode.CLIENT_IO_CLIENT_DISCONNECT:
            error.value = createErrorMessage(
              ErrSource.CLIENT,
              ErrorCode.CLIENT_IO_CLIENT_DISCONNECT,
              'Client disconnected itself',
              'setupSocketListeners.on:STANDARD.CLIENT.DISCONNECT:CLIENT_IO_CLIENT_DISCONNECT',
              MODULE_NAME,
              currentRoom.value?.id,
              currentUser.value?.id
            )
            break;
          case ErrorCode.CLIENT_IO_SERVER_DISCONNECT:
            cleanConnection()
            error.value = createErrorMessage(
              ErrSource.CLIENT,
              ErrorCode.CLIENT_IO_SERVER_DISCONNECT,
              'Server do disconnect',
              'setupSocketListeners.on:STANDARD.CLIENT.DISCONNECT:CLIENT_IO_SERVER_DISCONNECT',
              MODULE_NAME,
              currentRoom.value?.id,
              currentUser.value?.id
            )
            break;
          case ErrorCode.CLIENT_PING_TIMEOUT:
            console.log('Ping timeout.');
            // 网络问题，客户端会自动尝试重连（除非禁用了自动重连）
            break;
          case ErrorCode.CLIENT_TRANSPORT_CLOSE:
            console.log('Transport closed.');
            // 网络问题，客户端会自动尝试重连
            break;
          case ErrorCode.CLIENT_TRANSPORT_ERROR:
            console.log('Transport error.');
            // 网络问题，客户端会自动尝试重连，并可能记录详细错误信息
            break;
          case ErrorCode.CLIENT_PARSE_ERROR:
            console.log('parse error.');
            break;
          default:
            console.log('Unknown disconnection reason.');
          // 其他未知原因，通常也尝试重连
        }
      });

      socket.on(WebSocketEvents.STANDARD.CLIENT.RECONNECTING, (attemptNumber) => {
        const method = 'setupSocketListeners::on(WebSocketEvents.STANDARD.CLIENT.RECONNECTING)'
        isConnecting.value = true;
        const cliErr = createCliErr(
          ErrorCode.CLIENT_RECONNECTING,
          'Client Reconnecting, attempt ' + attemptNumber,
          method,
          MODULE_NAME,
          currentRoom.value?.id,
          currentUser.value?.id
        )
        error.value = cliErr__errPayload(cliErr, method, MODULE_NAME)
      });

      socket.on(WebSocketEvents.STANDARD.CLIENT.RECONNECT_FAILED, () => {
        const method = 'setupSocketListeners::on(WebSocketEvents.STANDARD.CLIENT.RECONNECT_FAILED)'
        cleanConnectionAndApiLeaveRoom(false)

        const cliErr = createCliErr(
          ErrorCode.RECONNECT_FAILED,
          'Reconnect failed! Please try again later',
          method,
          MODULE_NAME,
          currentRoom.value?.id,
          currentUser.value?.id
        )
        error.value = cliErr__errPayload(cliErr, method, MODULE_NAME)
      });

      socket.on(WebSocketEvents.STANDARD.CLIENT.RECONNECT_ERROR, (err: Error) => {
        const method = 'setupSocketListeners::on(WebSocketEvents.STANDARD.CLIENT.RECONNECT_ERROR)'
        cleanConnectionAndApiLeaveRoom(false)

        const cliErr = unknown__cliErr(err, ErrorCode.RECONNECT_ERROR, method, MODULE_NAME)
        error.value = cliErr__errPayload(cliErr, method, MODULE_NAME)
      });

      socket.on(WebSocketEvents.STANDARD.CLIENT.RECONNECT, async () => {
        isConnected.value = true;
        isConnecting.value = false;

        const method = 'setupSocketListeners::on(WebSocketEvents.STANDARD.CLIENT.RECONNECT)'
        try {
          const res = await apiIsInRoom()
          if (res) {
            switch (res) {
              case ErrorCode.OK:
                break
              case ErrorCode.USER_NOT_IN_ROOM:
                joinRoom(currentRoom.value?.id, false)
                break
              case ErrorCode.ROOM_NOT_FOUND:
                throw createCliErr(
                  ErrorCode.ROOM_NOT_FOUND,
                  'Room is NOT Found when reconnected, please choose another room',
                  method,
                  MODULE_NAME,
                  currentRoom.value?.id,
                  currentUser.value?.id
                )
              default:
                break
            }
          }
        } catch (err) {
          error.value = cliErr__errPayload(unknown__cliErr(err), method, MODULE_NAME)
        }

      });

      // required 获取底层传输错误详细信息
      socket.on(WebSocketEvents.STANDARD.CLIENT.CONNECT_ERROR, (err) => {
        const method = 'setupSocketListeners::on(WebSocketEvents.STANDARD.CLIENT.CONNECT_ERROR)'
        const cliErr = unknown__cliErr(err, ErrorCode.CONNECT_ERROR, method, MODULE_NAME)
        error.value = cliErr__errPayload(cliErr, method, MODULE_NAME)
      });

      socket.on(WebSocketEvents.STANDARD.CLIENT.PONG, (latency) => {
        latencies.push(latency)
      });
    }

    /* Room */
    const setupRoomListeners = () => {
      // required
      socket.on(WebSocketEvents.WARNNING.ROOM.IDLE, (data: SystemPayload) => {
        const { roomId, sysMsg } = data
        if (roomId === currentRoom.value!.id) {
          messages.value.set(sysMsg.id, sysMsg)
        }

        logger.warn(sysMsg.content, {
          module: MODULE_NAME,
          timestamp: getTimestamp(),
          details: {
            roomId
          }
        })
      })

      // required
      socket.on(WebSocketEvents.ROOM.UPDATE, (payload: RoomUpdatedPayload) => {

        // for server broadcast
        if (payload.leavedUserId) {
          const leavedUser = currentRoom.value?.users.find(user => user.id === payload.leavedUserId)
          if (leavedUser) {
            const leftSysMsg = createSystemMessage(
              SystemMessageTemplate.LEFT(leavedUser.name),
              payload.roomId
            )
            messages.value.set(leftSysMsg.id, leftSysMsg)
          }
        } else if (payload.joinedUserId) {
          const joinedUser = payload.users.find(user => user.id === payload.joinedUserId)
          if (joinedUser) {
            const joinedSysMsg = createSystemMessage(
              SystemMessageTemplate.JOINED(joinedUser.name),
              payload.roomId
            )
            messages.value.set(joinedSysMsg.id, joinedSysMsg)
          }
        } else if (payload.kickedUserIds) {
          payload.kickedUserIds.forEach(kickee => {
            const kickedUser = currentRoom.value?.users.find(user => user.id === kickee)
            if (kickedUser) {
              const kickSysMsg = createSystemMessage(
                SystemMessageTemplate.KICKED(kickedUser.name),
                payload.roomId
              )
              messages.value.set(kickSysMsg.id, kickSysMsg)
            }
          })
        }

        currentRoom.value = {
          id: payload.roomId,
          users: payload.users,
          createdAt: payload.createdAt ?? currentRoom.value!.createdAt,
          status: payload.status,
          adminId: payload.adminId ?? currentRoom.value!.adminId
        };

      });

      // required 
      // invitee receive invite
      socket.on(WebSocketEvents.ACTION.SERVER.INVITE, (payload: InvitePayload, callback: (ack: AckPayload) => void) => {
        const { userId, userName, invitorLang } = payload
        inviteAck.value = null
        inviteInfo.value = `${userName ? userName : 'Someone of'} ${invitorLang ? `[${invitorLang.name}]` : ''} invite you to join a room?`
        
        watch(inviteAck, (newVal) => {
          if (newVal !== null) {
            callback({
              ack: newVal,
              timestamp: getTimestamp()
            })
          }
        })
      })
    }

    /* USER */
    const setupUserListeners = () => {
      // required
      socket.on(WebSocketEvents.USER.KICKED, (data: UserKickedPayload) => {
        const { roomId, user, kickee, isKicked } = data
        if (roomId === currentRoom.value!.id && isKicked && kickee === currentUser.value?.id) {
          messages.value.set(user.id, createSystemMessage(
            SystemMessageTemplate.KICKED(currentUser.value!.name),
            roomId
          ))

          logger.info(`'Room Leaved, RoomId: ${data.roomId}'`, {
            module: MODULE_NAME,
            timestamp: getTimestamp(),
            details: {
              ...data
            }
          })

          cliSocket?.off(WebSocketEvents.ERROR.ROOM.LEAVE);

          cleanConnectionAndApiLeaveRoom(true, false, false);
        }
      })
    }

    /* MSG */
    const setupMsgListeners = () => {
      // required
      socket.on(WebSocketEvents.ERROR.MESSAGE.SEND_FAILED, (err: ErrorPayload) => {
        messages.value.set(err.errMsg.id, err.errMsg);
        const originalMsgId: string | undefined = err.details && err.details.messageId ? err.details.messageId as string : undefined
        const checkAt: number | undefined = err.details && err.details.fstCheckAt ? err.details.fstCheckAt as number : undefined
        const originalMsg: TextMessageWithState | undefined = originalMsgId ? messages.value.get(originalMsgId) as TextMessageWithState : undefined;
        if (originalMsg) {
          originalMsg.state.fstCheckAt ?
            doNothing() :
            checkAt ?
              (() => {
                originalMsg.timestamp = checkAt
                originalMsg.state.fstCheckAt = checkAt
              })() : doNothing()
          originalMsg.state.state = EMessageState.FAILED;
          originalMsg.state.stateAt = err.timestamp;
        }
        error.value = err;
      });

      /* MESSAGE */
      // required
      socket.on(WebSocketEvents.MESSAGE.SEND.DELIVERED, async ({ messageWithState }: MessageDeliveredPayload) => {

        if (messageWithState.type === EMessageType.TEXT
          && enableTranslate.value
          && messageWithState.sender.id !== currentUser.value?.id
          && messageWithState.sender.lang !== currentUser.value?.lang
        ) {
          const res: string | ErrorPayload = await translate(
            messageWithState.sender.lang.source,
            currentUser.value!.lang.target,
            messageWithState.content
          )

          if (typeof res === 'string') {
            messageWithState.translatedContent = res
          } else {
            error.value = res
          }
        }

        messages.value.set(messageWithState.id, messageWithState)

        logger.info('Message Delivered', {
          module: MODULE_NAME,
          timestamp: getTimestamp(),
          details: {
            ...messageWithState
          }
        })

        if (currentRoom.value) {
          socket.emit(WebSocketEvents.MESSAGE.SEND.ACK, {
            timestamp: getTimestamp(),
            roomId: currentRoom.value!.id,
          });
        }
      });

      // required
      socket.on(WebSocketEvents.MESSAGE.HISTORY.OFFER, (payload: HistorySharePayload) => {
        const { roomId, histories } = payload;
        if (roomId !== currentRoom.value?.id) return;

        histories.forEach((history) => {
          messages.value.set(history.id, history);
        });
      });
    }

    /* ERROR */
    const setupOtherListeners = () => {
      // required
      socket.on(WebSocketEvents.ERROR.UNKNOWN, (data: ErrorPayload) => {
        error.value = data;
      });
      /* ACTION */
      socket.on(WebSocketEvents.ACTION.SERVER.FORCEDISCONNECT, ({ roomId }: SystemPayload) => {
        if (roomId === currentRoom.value!.id) {
          needLeave.value = false
          cleanConnection()
        }
      });

      // socket.on(WebSocketEvents.SERVER.STATUS, (sspl: ServerStatusPayload) => {
      //   if (sspl.userId === currentUser.value!.id) {
      //     population.value = sspl.population
      //     randomPopulation.value = sspl.randomModeCount
      //   }
      // });

      socket.on(WebSocketEvents.ROOM.RANDOM.ACK, (data: ServerMatchRandomPayload) => {
        const { ack, roomId, isInvitor, userToken } = data;
        const method = 'setupSocketListeners.setupOtherListeners.room.random.ack'

        try {
          if (ack === EServerAckType.CONFIRM) {
            if (userToken && isInvitor) currentUserToken = userToken;
            randomRoomId.value = roomId;
            if (isInvitor) joinRoom(roomId, true, false, undefined, isInvitor);
          } else {
            throw createCliErr(
              ErrorCode.ROOM_MATCH_RANDOM_FAILED,
              'No one accept your invitation',
              method,
              MODULE_NAME,
              currentRoom.value?.id,
              currentUser.value?.id
            )
          }
        } catch (err) {
          error.value = catchErr(err, method, MODULE_NAME);
        }
      })
    }

    setupStandardListeners()
    setupRoomListeners()
    setupUserListeners()
    setupMsgListeners()
    setupOtherListeners()

    isListenersSetup = true
  }

  async function connect(): Promise<boolean> {

    const method = 'connect'
    isConnecting.value = true

    try {
      if (currentUser.value) {
        if (currentUser.value?.lang.source !== selectedSourceLangCode.value
          && currentUser.value?.lang.target !== selectedTargetLangCode.value
        ) {
          currentUser.value.lang = {
            target: selectedTargetLangCode.value,
            source: selectedSourceLangCode.value,
            name: getLanguageName(selectedTargetLangCode.value)
          }
        }
      } else {
        throw createCliErr(
          ErrorCode.CONNECT_CLIENT_ERROR,
          'User is null when connecting, please LOGIN first',
          method,
          MODULE_NAME,
          currentRoom.value?.id
        )
      }

      if (cliSocket.connected) {
        !isListenersSetup && setupSocketListeners(cliSocket)
        if (!isConnected.value) {
          isConnecting.value = false
          isConnected.value = true;
        }
        return Promise.resolve(true);
      }

      cliSocket.auth = GoogleAuth.getToken()
      cliSocket.connect();
      !isListenersSetup && setupSocketListeners(cliSocket)

      return await new Promise<boolean>((resolve, reject) => {
        const timeout = setTimeout(() => {
          cliSocket!.off(WebSocketEvents.STANDARD.CLIENT.CONNECT);

          const err = createCliErr(
            ErrorCode.CONNECT_TIMEOUT,
            'Client Connect To Server Timeout',
            method,
            MODULE_NAME,
            currentRoom.value?.id,
            currentUser.value?.id
          )
          reject(err);
        }, cliConfig.connection.connectTimeout);

        cliSocket!.once(WebSocketEvents.STANDARD.CLIENT.CONNECT, () => {
          clearTimeout(timeout);
          isConnecting.value = false
          isConnected.value = true;

          logger.info('Client Connect Success', {
            module: MODULE_NAME,
            timestamp: getTimestamp(),
            method,
            details: {
              currentUser: currentUser.value
            }
          })

          resolve(true);
        })
      })
    } catch (err) {
      error.value = catchErr(err, 'connect', MODULE_NAME)
      return Promise.reject(false)
    }
  }

  async function joinRoom(
    roomId?: number,
    cleanRoomWhenJoinFailed = true,
    isReenter = false,
    caller?: string,
    isInvitor?: boolean
  ) {
    const method = 'joinRoom'
    const joinRoomCliErr = (code: ErrorCode, message: string) => createCliErr(
      code,
      message,
      method,
      MODULE_NAME,
      roomId,
      currentUser.value?.id
    )

    // TODO: 通过chatmode, 来创建/加入/邀请, 如果用户已经在房间中, 如果默认为随机,  当用户退出时, 另外的用户也收到退出通知
    /*
    1, 随机模式, 点击start, 从radomModeUser中随机抽取用户, 对该用户发送邀请, 
      用户每5s只接受被邀请1次, 
        需要标记用户的邀请次数
      Key的大小对于内存的影响, 优化用户key
    2, 用户接受邀请, 创建者创建房间, 被邀请者加入房间
    3, 聊天内容的审查
    */
    try {

      if (!cliSocket) {
        throw joinRoomCliErr(
          ErrorCode.CONNECT_CLIENT_ERROR,
          'Client Socket is NULL when joining room'
        )
      }

      if (!isConnected.value) {
        throw joinRoomCliErr(
          ErrorCode.CONNECT_ERROR,
          'Client is NOT Connected when joining room'
        )
      }

      if (!currentUser.value) {
        throw joinRoomCliErr(
          ErrorCode.USER_NOT_EXISTS,
          'User is NULL when joining room'
        )
      }

      logger.info(`'Client Try to Join Room, RoomId: ${roomId}'`, {
        module: MODULE_NAME,
        timestamp: getTimestamp(),
        method,
        details: {
          ...currentUser.value,
          caller,
          cleanRoomWhenJoinFailed,
          isReenter
        }
      })

      const jrpl: JoinRoomPayload = {
        roomId: roomId ?? 0,
        user: currentUser.value,
        timestamp: getTimestamp(),
        reenter: isReenter,
        userToken: currentUserToken,
        chatMode: chatMode.value,
        isInvitor
      };
      cliSocket!.emit(WebSocketEvents.ROOM.JOIN, jrpl);

      // 等待服务器响应
      await new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(joinRoomCliErr(
            ErrorCode.CONNECT_ERROR,
            'Join Room timeout'
          ))
        }, 50000);

        const handleRoomJoined = (data: UserJoinedPayload) => {
          errListenersCleanup();
          clearTimeout(timeout);

          if ((!currentRoom.value || currentRoom.value?.id === data.room.id) && data.isJoin) {
            logger.info(`'Room Joined, RoomId: ${data.room.id}'`, {
              module: MODULE_NAME,
              timestamp: getTimestamp(),
              method,
              details: {
                ...data
              }
            })
          }

          currentRoom.value = data.room
          currentUserToken = data.userToken
          resolve();
        };

        const handleErrorFromServer = (err: ErrorPayload) => {
          clearTimeout(timeout);
          errListenersCleanup();

          cliSocket?.off(WebSocketEvents.ROOM.JOINED);

          isInRoom.value = false;
          cleanRoomWhenJoinFailed && (currentRoom.value = null);
          error.value = err

          reject(err);
        };

        const errListenersCleanup = () => {
          cliSocket?.off(WebSocketEvents.ERROR.ROOM.FULL);
          cliSocket?.off(WebSocketEvents.ERROR.ROOM.JOIN);
        };

        cliSocket?.once(WebSocketEvents.ROOM.JOINED, handleRoomJoined);
        cliSocket?.once(WebSocketEvents.ERROR.ROOM.FULL, handleErrorFromServer);
        cliSocket?.once(WebSocketEvents.ERROR.ROOM.JOIN, handleErrorFromServer);

      });
    } catch (err) {
      error.value = catchErr(err, method, MODULE_NAME)
    }
  }

  async function leaveRoom() {
    const method = 'leaveRoom';
    const leaveRoomCliErr = (code: ErrorCode, message: string) => createCliErr(
      code,
      message,
      method,
      MODULE_NAME,
      currentRoom.value?.id,
      currentUser.value?.id
    )

    try {
      if (!cliSocket) {
        throw leaveRoomCliErr(
          ErrorCode.CONNECT_CLIENT_ERROR,
          'Client Socket is NULL when leaving room',
        )
      }

      if (!isConnected.value) {
        throw leaveRoomCliErr(
          ErrorCode.CONNECT_ERROR,
          'Client is NOT Connected when leaving room'
        );
      }


      if (!currentUser.value) {
        throw leaveRoomCliErr(
          ErrorCode.USER_NOT_EXISTS,
          'User is NULL when leaving room'
        );
      }

      if (!currentRoom.value) {
        throw leaveRoomCliErr(
          ErrorCode.ROOM_NOT_FOUND,
          'Room is NULL when leaving room'
        );
      }

      const payload: LeaveRoomPayload = {
        roomId: currentRoom.value.id,
        user: currentUser.value,
        timestamp: getTimestamp(),
        isClient: true
      };
      cliSocket?.emit(WebSocketEvents.ROOM.LEAVE, payload);

      new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          cliSocket?.off(WebSocketEvents.ERROR.ROOM.LEAVE);
          cliSocket?.off(WebSocketEvents.ROOM.LEFT);

          reject(leaveRoomCliErr(
            ErrorCode.CONNECT_ERROR,
            'Leave Room Timeout'
          ))
        }, 50000);

        cliSocket?.once(WebSocketEvents.ERROR.ROOM.LEAVE, (err: ErrorPayload) => {
          clearTimeout(timeout);
          cliSocket?.off(WebSocketEvents.ROOM.LEFT);

          reject(err);
        });

        cliSocket?.once(WebSocketEvents.ROOM.LEFT, (data: UserLeftPayload) => {
          clearTimeout(timeout);

          logger.info(`'Room Leaved, RoomId: ${data.roomId}'`, {
            module: MODULE_NAME,
            timestamp: getTimestamp(),
            details: {
              ...data
            }
          })

          cliSocket?.off(WebSocketEvents.ERROR.ROOM.LEAVE);

          if (currentUser.value && data.isLeft && data.leftUserId === currentUser.value.id) {
            cleanConnectionAndApiLeaveRoom(true, false, false)
          }

          resolve();
        });
      });

    } catch (err) {
      error.value = catchErr(err, method, MODULE_NAME)
    }
  }

  async function sendMessage(content: string, roomId: number, resend?: TextMessageWithState) {
    const method = 'sendMessage';
    const sendMessageCliErr = (code: ErrorCode, message: string) => createCliErr(
      code,
      message,
      method,
      MODULE_NAME,
      roomId,
      currentUser.value?.id
    )

    try {
      if (!isConnected.value) {
        throw sendMessageCliErr(
          ErrorCode.CONNECT_ERROR,
          'Client is NOT Connected when sending message',
        );
      }

      if (!currentRoom.value) {
        throw sendMessageCliErr(
          ErrorCode.ROOM_NOT_FOUND,
          'Room is NULL when sending message',
        )
      }

      if (currentRoom.value.id !== roomId) {
        throw sendMessageCliErr(
          ErrorCode.ROOM_NOT_MATCH,
          `Current RoomId: ${currentRoom.value?.id} does not match the target RoomId: ${roomId} when sending message`,
        );
      }

      if (!currentUser.value) {
        throw sendMessageCliErr(
          ErrorCode.USER_NOT_EXISTS,
          'User is NULL when sending message',
        );
      }

      const msgContent = resend ? resend.content : content;
      if (!msgContent) {
        throw sendMessageCliErr(
          ErrorCode.MESSAGE_CONTENT_NULL,
          'Content is NULL when sending message'
        );
      }

      const timestamp = getTimestamp()
      const msgWithState: Message = resend ? resend : {
        id: genUserMsgId(currentRoom.value.id, currentUser.value!.id),
        roomId,
        type: EMessageType.TEXT,
        content,
        sender: currentUser.value!,
        timestamp,
        state: {
          state: EMessageState.SENDING,
          stateAt: timestamp
        }
      };

      logger.info('Send Message', {
        module: MODULE_NAME,
        timestamp,
        details: {
          ...msgWithState
        }
      })

      messages.value.set(msgWithState.id, msgWithState);

      const payload: SendMessagePayload = {
        timestamp,
        roomId,
        userId: currentUser.value!.id,
        messageId: msgWithState.id,
        content: msgContent,
        lang: {
          source: selectedSourceLangCode.value,
          target: selectedTargetLangCode.value,
          name: getLanguageName(selectedTargetLangCode.value)
        },
        fstSendTimestamp: resend ? resend.timestamp : undefined
      };

      cliSocket?.emit(WebSocketEvents.MESSAGE.SEND.SENDING, payload);
    } catch (err) {
      error.value = catchErr(err, method, MODULE_NAME)
    }
  }

  function shareHistory(roomId: number, targetUserId: string) {
    try {
      if (!cliSocket) {
        throw createCliErr(
          ErrorCode.CONNECT_CLIENT_ERROR,
          'Client Socket is NULL when sharing history',
          'shareHistory',
          MODULE_NAME,
          roomId,
          currentUser.value?.id,
          undefined,
          { targetUserId }
        );
      } else if (!currentUser.value) {
        throw createCliErr(
          ErrorCode.CONNECT_CLIENT_ERROR,
          'User is NULL when sharing history',
          'shareHistory',
          MODULE_NAME,
          roomId,
          undefined,
          undefined,
          { targetUserId }
        );
      };

      const sharePayload: HistorySharePayload = {
        roomId,
        fromUserId: currentUser.value!.id,
        targetUserId,
        histories: Array.from(messages.value.values()).filter((msg: Message) => msg.type === EMessageType.TEXT),
        timestamp: getTimestamp()
      }

      cliSocket!.emit(WebSocketEvents.MESSAGE.HISTORY.SHARE, sharePayload);
    } catch (err) {
      error.value = catchErr(err, 'shareHistory', MODULE_NAME)
    }
  }

  // kick user
  function kickUser(kickeeIds: string[]) {
    const method = 'kickUser';
    const kickCliErr = (code: ErrorCode, message: string, details?: Record<string, unknown>) => createCliErr(
      code,
      message,
      method,
      MODULE_NAME,
      currentRoom.value?.id,
      currentUser.value?.id,
      undefined,
      details
    )

    try {
      if (!cliSocket) {
        throw kickCliErr(
          ErrorCode.CONNECT_CLIENT_ERROR,
          'Client Socket is NULL when kicking user',
        );
      } else if (!currentUser.value) {
        throw kickCliErr(
          ErrorCode.CONNECT_CLIENT_ERROR,
          'User is NULL when kicking user'
        );
      };

      kickeeIds.forEach((kickeeId) => {
        let kickee = currentRoom.value?.users.find((user) => user.id === kickeeId)
        if (!kickee) {
          throw kickCliErr(
            ErrorCode.USER_NOT_IN_ROOM,
            `User ${kickeeId} not in the room ${currentRoom.value?.id} when kicking user`,
            { kickeeId, kickee }
          );
        }
      })

      const kpl: KickUserPayload = {
        timestamp: getTimestamp(),
        roomId: currentRoom.value!.id,
        kickee: kickeeIds,
        user: currentUser.value!,
        userToken: currentUserToken
      }

      cliSocket!.emit(WebSocketEvents.USER.KICK, kpl);
    } catch (err) {
      error.value = catchErr(err, 'kickUser', MODULE_NAME)
    }
  }


  async function switchChatMode(isRandom: boolean = false): Promise<boolean> {
    const method = 'switchChatMode';
    const modeSwitchErr = (code: ErrorCode, message: string) => createCliErr(
      code,
      message,
      method,
      MODULE_NAME
    )

    try {
      if (!currentUser.value) {
        throw modeSwitchErr(
          ErrorCode.CONNECT_CLIENT_ERROR,
          'User is null when switch chat mode, please LOGIN first',
        )
      }

      const timestamp = getTimestamp();
      const cMode = isRandom ? EChatModeType.RANDOM : EChatModeType.NORMAL;
      logger.info('Switching chat mode', { module: MODULE_NAME, method, timestamp, details: { newMode: cMode } });

      const cpl: ClientPayload = {
        timestamp,
        userId: currentUser.value.id,
        chatMode: cMode
      };

      const ack = await cliSocket?.emitWithAck(WebSocketEvents.SETTINGS, cpl)
      if (ack.ack === EServerAckType.CONFIRM) {
        chatMode.value = cMode
        return true;
      }
    } catch (err) {
      error.value = catchErr(err, method, MODULE_NAME)
    }
    return false;
  }

  async function checkServerStatus(mode: EChatModeType) {
    const method = 'checkServerStatus';
    const statusCheckErr = (code: ErrorCode, message: string) => createCliErr(
      code,
      message,
      method,
      MODULE_NAME
    )
    try {
      if (!cliSocket) {
        throw statusCheckErr(
          ErrorCode.CONNECT_CLIENT_ERROR,
          'Socket is not initialized',
        );
      }
      if (!currentUser.value) {
        throw statusCheckErr(
          ErrorCode.CONNECT_CLIENT_ERROR,
          'User is not logged in',
        );
      }

      const timestamp = getTimestamp();
      const cpl: ClientPayload = {
        timestamp,
        userId: currentUser.value.id,
        chatMode: mode
      };

      const ackStatus:ServerStatusPayload = await cliSocket.emitWithAck(WebSocketEvents.SERVER.STATUS, cpl);
      if (ackStatus?.ack === EServerAckType.CONFIRM) {
        if (ackStatus.userId === currentUser.value.id) {
          population.value = ackStatus.population;
          randomPopulation.value = ackStatus.randomModeCount;
        }
      }
    } catch (err) {
      error.value = catchErr(err, method, MODULE_NAME);
    }
  }

  /**
   * 用户切换为random模式后, 发送匹配请求
   * 返回roomid, 执行join room
   */
  async function matchRandom() {
    const method = 'matchRandom'
    const matchRandomErr = (code: ErrorCode, message: string) => createCliErr(
      code,
      message,
      method,
      MODULE_NAME,
      undefined,
      currentUser.value?.id
    )

    try {
      if (!cliSocket) {
        throw matchRandomErr(
          ErrorCode.CONNECT_CLIENT_ERROR,
          'Client Socket is NULL when matching random room',
        )
      }

      if (!currentUser.value) {
        throw matchRandomErr(
          ErrorCode.CONNECT_CLIENT_ERROR,
          'User is NULL when matching random room',
        )
      }

      // clear invite info
      inviteStatus.value = null;
      inviteAck.value = null;

      const timestamp = getTimestamp();

      /**
       * send random request
       * listen event: WebSocketEvents.ROOM.RANDOM.ACK
       */
      cliSocket.emit(WebSocketEvents.ROOM.RANDOM.MATCH, {
        timestamp,
        userId: currentUser.value.id,
        chatMode: chatMode.value,
        userToken: currentUserToken
      })

      const matchFailed = (isInvitor?: boolean) => {
        error.value = {
          code: ErrorCode.ROOM_MATCH_RANDOM_FAILED,
          message: '随机匹配失败，请稍后重试',
          source: ErrSource.CLIENT,
          module: MODULE_NAME,
          method: 'connectRandomRoom',
          timestamp: getTimestamp()
        };
        isConnecting.value = false;
      }

      const matchRandomTimeout = setTimeout(() => {
        clearTimeout(matchRandomTimeout);

        cliSocket?.off(WebSocketEvents.ROOM.RANDOM.ACK);
        matchFailed()
      }, cliConfig.chat.perInviteTimeout * cliConfig.chat.randomInviteCount);

      cliSocket.once(WebSocketEvents.ROOM.RANDOM.ACK, (data: ServerMatchRandomPayload) => {
        const { ack, roomId, isInvitor, userToken } = data;
        clearTimeout(matchRandomTimeout);
        
        if (ack === EServerAckType.CONFIRM) {
          // 更新 userToken（不管是邀请者还是被邀请者）
          if (userToken) currentUserToken = userToken;
          
          // 加入房间，标记为随机模式
          joinRoom(roomId, true, false, 'connectRandomRoom', isInvitor);
        } else {
          matchFailed(isInvitor);
        }
      })

    } catch (err) {
      error.value = catchErr(err, method, MODULE_NAME);
    }
  }

  return {
    // 状态
    isConnected,
    isConnecting,
    currentUser,
    currentRoom,
    messages,
    error,
    isInRoom,
    needLeave,
    isListenersSetup,
    avgLatency,
    maxLatency,
    minLatency,
    enableTranslate,
    selectedTargetLangCode,
    selectedSourceLangCode,
    population,
    randomPopulation,
    randomRoomId,
    chatMode,
    inviteInfo,
    inviteAck,

    // 方法
    connect,
    sendMessage,
    joinRoom,
    leaveRoom,
    shareHistory,
    handleGoogleLogin,
    handleLogout,
    kickUser,
    createUser,
    switchChatMode,
    checkServerStatus,
    matchRandom
  };
});

export { useChatStore };
