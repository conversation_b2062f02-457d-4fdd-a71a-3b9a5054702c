# UniBabble Server Environment Configuration

# Server Configuration
NODE_ENV=development
PORT=3000

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_secure_password
REDIS_DB=0
REDIS_MAX_RETRIES=3
REDIS_RETRY_DELAY=1000
REDIS_CONNECT_TIMEOUT=10000
REDIS_COMMAND_TIMEOUT=5000

# Cache Configuration
CACHE_LOCAL_TTL=300000
CACHE_REDIS_TTL=1800
CACHE_MAX_LOCAL_SIZE=1000

# Fallback Configuration
FALLBACK_ENABLED=true
FALLBACK_MAX_MEMORY_SIZE=10000

# Monitoring Configuration
HEALTH_CHECK_INTERVAL=30000
PERFORMANCE_MONITOR_INTERVAL=600000

# Redis Logging Configuration
REDIS_LOGGING_ENABLED=true

# Log Level Configuration
LOG_DEBUG_ENABLED=true
LOG_DEBUG_RETENTION_DAYS=1
LOG_INFO_ENABLED=true
LOG_INFO_RETENTION_DAYS=7
LOG_WARN_ENABLED=true
LOG_WARN_RETENTION_DAYS=30
LOG_ERROR_ENABLED=true
LOG_ERROR_RETENTION_DAYS=90

# Log Batch Configuration
LOG_BATCH_SIZE=50
LOG_FLUSH_INTERVAL=5000

# Authentication Configuration (if enabled)
AUTH_ENABLE=false
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# CORS Configuration
CORS_ORIGIN=http://localhost:3000
CORS_CREDENTIALS=true

# Room Configuration
ROOM_MAX_IDLE=300000
ROOM_MAX_IDLE_CHECK_INTERVAL=60000
ROOM_FORCE_DISCONNECT_INTERVAL=60000

# Translation Configuration
TRANSLATE_API_KEY=your_translation_api_key
TRANSLATE_API_URL=https://api.translate.service.com

# Security Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Development Configuration (only for development)
DEBUG_ENABLED=true
VERBOSE_LOGGING=true
ENABLE_PROFILING=false
