/**
 * UniBabble Redis 日志系统 API 使用示例
 * 
 * 本文件展示了如何使用日志系统的各种 API 接口
 */

const axios = require('axios');

// 配置基础 URL
const BASE_URL = 'http://localhost:3000/api';

// 创建 axios 实例
const api = axios.create({
  baseURL: BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

/**
 * 1. 查询日志示例
 */
async function queryLogsExample() {
  console.log('\n=== 查询日志示例 ===');
  
  try {
    // 查询最近1小时的错误日志
    const response = await api.get('/logs/query', {
      params: {
        levels: 'ERROR',
        startTime: Date.now() - 60 * 60 * 1000, // 1小时前
        endTime: Date.now(),
        page: 1,
        pageSize: 20
      }
    });
    
    console.log('查询结果:', {
      total: response.data.data.total,
      entries: response.data.data.entries.length,
      hasNext: response.data.data.hasNext
    });
    
    // 显示前3条日志
    response.data.data.entries.slice(0, 3).forEach((entry, index) => {
      console.log(`${index + 1}. [${entry.level}] ${entry.message}`);
      console.log(`   时间: ${new Date(entry.timestamp).toISOString()}`);
      console.log(`   模块: ${entry.module || 'N/A'}`);
      if (entry.error) {
        console.log(`   错误: ${entry.error.message}`);
      }
      console.log('');
    });
    
  } catch (error) {
    console.error('查询日志失败:', error.response?.data || error.message);
  }
}

/**
 * 2. 获取日志统计示例
 */
async function getLogStatsExample() {
  console.log('\n=== 日志统计示例 ===');
  
  try {
    const response = await api.get('/logs/stats', {
      params: {
        startTime: Date.now() - 24 * 60 * 60 * 1000, // 最近24小时
        endTime: Date.now(),
        groupBy: 'hour'
      }
    });
    
    const stats = response.data.data;
    console.log('统计信息:');
    console.log(`  总日志数: ${stats.total}`);
    console.log(`  错误率: ${stats.errorRate.toFixed(2)}%`);
    console.log('  按级别分布:');
    Object.entries(stats.byLevel).forEach(([level, count]) => {
      console.log(`    ${level}: ${count}`);
    });
    
    console.log('  按模块分布:');
    Object.entries(stats.byModule).slice(0, 5).forEach(([module, count]) => {
      console.log(`    ${module}: ${count}`);
    });
    
  } catch (error) {
    console.error('获取统计失败:', error.response?.data || error.message);
  }
}

/**
 * 3. 获取健康状态示例
 */
async function getLogHealthExample() {
  console.log('\n=== 日志健康状态示例 ===');
  
  try {
    const response = await api.get('/logs/health');
    const health = response.data.data;
    
    console.log('健康状态:', health.status);
    console.log('总日志数:', health.totalEntries);
    
    if (health.issues && health.issues.length > 0) {
      console.log('发现问题:');
      health.issues.forEach((issue, index) => {
        console.log(`  ${index + 1}. ${issue}`);
      });
    }
    
    if (health.recommendations && health.recommendations.length > 0) {
      console.log('建议:');
      health.recommendations.forEach((rec, index) => {
        console.log(`  ${index + 1}. ${rec}`);
      });
    }
    
  } catch (error) {
    console.error('获取健康状态失败:', error.response?.data || error.message);
  }
}

/**
 * 4. 获取最近日志示例
 */
async function getRecentLogsExample() {
  console.log('\n=== 最近日志示例 ===');
  
  try {
    const response = await api.get('/logs/recent', {
      params: {
        count: 10,
        level: 'ERROR'
      }
    });
    
    const data = response.data.data;
    console.log(`最近 ${data.count} 条错误日志:`);
    
    data.entries.forEach((entry, index) => {
      const time = new Date(entry.timestamp).toLocaleString();
      console.log(`${index + 1}. [${time}] ${entry.message}`);
      if (entry.module) {
        console.log(`   模块: ${entry.module}`);
      }
    });
    
  } catch (error) {
    console.error('获取最近日志失败:', error.response?.data || error.message);
  }
}

/**
 * 5. 获取错误日志摘要示例
 */
async function getErrorSummaryExample() {
  console.log('\n=== 错误日志摘要示例 ===');
  
  try {
    const response = await api.get('/logs/errors', {
      params: {
        limit: 20
      }
    });
    
    const summary = response.data.data;
    console.log(`总错误数: ${summary.totalErrors}`);
    
    if (summary.errorPatterns && summary.errorPatterns.length > 0) {
      console.log('\n错误模式 (Top 5):');
      summary.errorPatterns.slice(0, 5).forEach((pattern, index) => {
        console.log(`  ${index + 1}. ${pattern.pattern}: ${pattern.count} 次`);
      });
    }
    
    if (summary.errorsByModule && summary.errorsByModule.length > 0) {
      console.log('\n按模块分布:');
      summary.errorsByModule.slice(0, 5).forEach((module, index) => {
        console.log(`  ${index + 1}. ${module.module}: ${module.count} 次`);
      });
    }
    
  } catch (error) {
    console.error('获取错误摘要失败:', error.response?.data || error.message);
  }
}

/**
 * 6. 执行清理示例
 */
async function cleanupLogsExample() {
  console.log('\n=== 日志清理示例 ===');
  
  try {
    const response = await api.post('/logs/cleanup');
    const result = response.data.data;
    
    console.log('清理结果:');
    console.log(`  删除条目数: ${result.deletedEntries}`);
    console.log(`  删除字节数: ${result.deletedBytes}`);
    console.log(`  处理时长: ${result.duration}ms`);
    console.log(`  处理级别: ${result.processedLevels.join(', ')}`);
    
    if (result.errors && result.errors.length > 0) {
      console.log('清理错误:');
      result.errors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error}`);
      });
    }
    
  } catch (error) {
    console.error('执行清理失败:', error.response?.data || error.message);
  }
}

/**
 * 7. 强制刷新缓冲区示例
 */
async function flushBuffersExample() {
  console.log('\n=== 刷新缓冲区示例 ===');
  
  try {
    const response = await api.post('/logs/flush');
    console.log('刷新结果:', response.data.message);
    
  } catch (error) {
    console.error('刷新缓冲区失败:', error.response?.data || error.message);
  }
}

/**
 * 8. 复杂查询示例
 */
async function complexQueryExample() {
  console.log('\n=== 复杂查询示例 ===');
  
  try {
    // 查询特定时间范围内特定模块的警告和错误日志
    const response = await api.get('/logs/query', {
      params: {
        startTime: Date.now() - 7 * 24 * 60 * 60 * 1000, // 7天前
        endTime: Date.now(),
        levels: 'WARN,ERROR',
        modules: 'UserService,AuthService',
        keyword: 'database',
        page: 1,
        pageSize: 50,
        sortOrder: 'desc'
      }
    });
    
    const result = response.data.data;
    console.log('复杂查询结果:');
    console.log(`  匹配条目: ${result.total}`);
    console.log(`  当前页: ${result.page}/${result.totalPages}`);
    console.log(`  返回条目: ${result.entries.length}`);
    
    // 按模块分组显示
    const byModule = {};
    result.entries.forEach(entry => {
      const module = entry.module || 'Unknown';
      if (!byModule[module]) {
        byModule[module] = [];
      }
      byModule[module].push(entry);
    });
    
    console.log('\n按模块分组:');
    Object.entries(byModule).forEach(([module, entries]) => {
      console.log(`  ${module}: ${entries.length} 条`);
    });
    
  } catch (error) {
    console.error('复杂查询失败:', error.response?.data || error.message);
  }
}

/**
 * 9. 性能测试示例
 */
async function performanceTestExample() {
  console.log('\n=== 性能测试示例 ===');
  
  const testCases = [
    { name: '小范围查询', params: { startTime: Date.now() - 60 * 60 * 1000, pageSize: 10 } },
    { name: '中等范围查询', params: { startTime: Date.now() - 6 * 60 * 60 * 1000, pageSize: 50 } },
    { name: '大范围查询', params: { startTime: Date.now() - 24 * 60 * 60 * 1000, pageSize: 100 } }
  ];
  
  for (const testCase of testCases) {
    try {
      const startTime = Date.now();
      const response = await api.get('/logs/query', { params: testCase.params });
      const duration = Date.now() - startTime;
      
      console.log(`${testCase.name}:`);
      console.log(`  响应时间: ${duration}ms`);
      console.log(`  返回条目: ${response.data.data.entries.length}`);
      console.log(`  总条目: ${response.data.data.total}`);
      console.log('');
      
    } catch (error) {
      console.error(`${testCase.name} 失败:`, error.response?.data || error.message);
    }
  }
}

/**
 * 主函数 - 运行所有示例
 */
async function runAllExamples() {
  console.log('UniBabble Redis 日志系统 API 示例');
  console.log('=====================================');
  
  try {
    await queryLogsExample();
    await getLogStatsExample();
    await getLogHealthExample();
    await getRecentLogsExample();
    await getErrorSummaryExample();
    await complexQueryExample();
    await performanceTestExample();
    
    // 管理操作（谨慎使用）
    console.log('\n=== 管理操作 ===');
    console.log('注意: 以下操作会修改系统状态，请谨慎使用');
    
    // await flushBuffersExample();
    // await cleanupLogsExample();
    
  } catch (error) {
    console.error('运行示例时发生错误:', error);
  }
  
  console.log('\n示例运行完成!');
}

/**
 * 单独运行特定示例
 */
async function runSpecificExample(exampleName) {
  const examples = {
    'query': queryLogsExample,
    'stats': getLogStatsExample,
    'health': getLogHealthExample,
    'recent': getRecentLogsExample,
    'errors': getErrorSummaryExample,
    'complex': complexQueryExample,
    'performance': performanceTestExample,
    'flush': flushBuffersExample,
    'cleanup': cleanupLogsExample
  };
  
  const example = examples[exampleName];
  if (example) {
    await example();
  } else {
    console.log('可用示例:', Object.keys(examples).join(', '));
  }
}

// 命令行参数处理
const args = process.argv.slice(2);
if (args.length > 0) {
  runSpecificExample(args[0]);
} else {
  runAllExamples();
}

// 导出函数供其他模块使用
module.exports = {
  queryLogsExample,
  getLogStatsExample,
  getLogHealthExample,
  getRecentLogsExample,
  getErrorSummaryExample,
  cleanupLogsExample,
  flushBuffersExample,
  complexQueryExample,
  performanceTestExample,
  runAllExamples,
  runSpecificExample
};
