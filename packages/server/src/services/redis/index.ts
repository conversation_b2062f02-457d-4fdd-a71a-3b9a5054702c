import type { RedisServiceHealthResult, RedisServiceStats } from '@/types/redis-health.js';

// 导出基础 Redis 服务
export { createRedisService } from './base.js';

// 导出房间服务
export * as roomService from './room/roomService.js';
export { default as roomServiceInstance } from './room/roomService.js';

// 导出用户服务
export * as userService from './user/userService.js';
export { default as userServiceInstance } from './user/userService.js';

// 导出随机用户服务
export * as randomUserService from './user/randomUserService.js';
export { default as randomUserServiceInstance } from './user/randomUserService.js';

// 导出统计服务
export * as statsService from './stats/statsService.js';
export { default as statsServiceInstance } from './stats/statsService.js';

// 导出 Redis 客户端
export { redis, getRedisClient, isRedisHealthy } from '@/utils/redis/client.js';

/**
 * Redis 服务集合
 * 提供统一的访问接口
 */
export const redisServices = {
  room: roomServiceInstance,
  user: userServiceInstance,
  randomUser: randomUserServiceInstance,
  stats: statsServiceInstance,
} as const;

/**
 * 检查所有 Redis 服务的健康状态
 */
export const checkRedisHealth = async (): Promise<RedisServiceHealthResult> => {
  try {
    const { isRedisHealthy } = await import('@/utils/redis/client.js');
    const isHealthy = await isRedisHealthy();

    return {
      isHealthy,
      services: {
        connection: isHealthy,
        room: isHealthy,
        user: isHealthy,
        randomUser: isHealthy,
        stats: isHealthy,
      }
    };
  } catch (error) {
    return {
      isHealthy: false,
      services: {
        connection: false,
        room: false,
        user: false,
        randomUser: false,
        stats: false,
      },
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

/**
 * 初始化所有 Redis 服务
 */
export const initializeRedisServices = async (): Promise<void> => {
  const health = await checkRedisHealth();
  if (!health.isHealthy) {
    throw new Error(`Redis services initialization failed: ${health.error}`);
  }

  console.log('Redis services initialized successfully');
};

/**
 * 清理所有过期数据
 */
export const cleanupExpiredData = async (): Promise<{
  stats: number;
  users: number;
  total: number;
}> => {
  const results = {
    stats: 0,
    users: 0,
    total: 0
  };

  try {
    // 清理过期统计数据
    results.stats = await redisServices.stats.cleanupExpiredStats();

    // 清理过期用户房间状态
    const userIds = await redisServices.user.getAllUserIds();
    for (const userId of userIds) {
      const cleaned = await redisServices.user.cleanupExpiredUserRoomStates(userId);
      results.users += cleaned;
    }

    results.total = results.stats + results.users;

    console.log(`Cleanup completed: ${results.total} expired entries removed`);
    return results;
  } catch (error) {
    console.error('Cleanup failed:', error);
    throw error;
  }
};

/**
 * 获取 Redis 服务统计信息
 */
export const getRedisServiceStats = async (): Promise<RedisServiceStats> => {
  try {
    const { redis } = await import('@/utils/redis/client.js');

    // 获取房间数量
    const roomIds = await redisServices.room.getAllRoomIds();

    // 获取用户数量
    const userIds = await redisServices.user.getAllUserIds();

    // 获取统计时间戳数量
    const timestamps = await redisServices.stats.getAllStatsTimestamps();

    // 获取内存使用情况
    let memory: string | undefined;
    try {
      const info = await redis.info('memory');
      const usedMemoryMatch = info.match(/used_memory:(\d+)/);
      if (usedMemoryMatch) {
        const usedMemoryBytes = parseInt(usedMemoryMatch[1], 10);
        memory = `${Math.round(usedMemoryBytes / 1024 / 1024 * 100) / 100} MB`;
      }
    } catch (error) {
      // 忽略内存信息获取失败
    }

    return {
      rooms: roomIds.length,
      users: userIds.length,
      statsTimestamps: {
        get: timestamps.get.length,
        post: timestamps.post.length
      },
      memory
    };
  } catch (error) {
    console.error('Failed to get Redis service stats:', error);
    throw error;
  }
};

// 默认导出服务集合
export default redisServices;
