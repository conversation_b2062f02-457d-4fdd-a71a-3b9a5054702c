import { createRedisService } from '../base.js';
import { loggingConfig, LogLevel, getLogLevelConfig } from '@/configs/logging.js';
import type {
  LogEntry,
  LogQueryParams,
  LogQueryResult,
  LogStatsParams,
  LogStatsResult,
  LogCleanupResult,
  LogIndexEntry
} from '@/types/logging.js';
import { logger, getTimestamp } from '@unibabble/shared';
import { v4 as uuidv4 } from 'uuid';
import zlib from 'zlib';
import { promisify } from 'util';

const MODULE_NAME = 'Server:services:redis:logging';
const NAMESPACE = loggingConfig.namespace;

// 压缩函数
const gzip = promisify(zlib.gzip);
const gunzip = promisify(zlib.gunzip);

const createLogService = () => {
  const redisService = createRedisService(NAMESPACE);
  const {
    getKey, set, get, del, exists, expire,
    hset, hget, hgetall, hdel, hkeys,
    zadd, zrem, zrange, zrevrange, zcard, zscore,
    sadd, srem, smembers, sismember,
    pipeline, executePipeline,
    scan, getAllKeys
  } = redisService;

  // 键生成函数
  const getLogEntryKey = (level: LogLevel, timestamp: number, entryId: string): string =>
    getKey('entries', level.toLowerCase(), Math.floor(timestamp / 3600000), entryId);

  const getLogIndexKey = (level: LogLevel, hour: number): string =>
    getKey('index', level.toLowerCase(), hour);

  const getLogStatsKey = (level: LogLevel, day: string): string =>
    getKey('stats', level.toLowerCase(), day);

  const getLogMetaKey = (): string => getKey('meta');

  /**
   * 写入单个日志条目
   */
  const writeLogEntry = async (entry: LogEntry): Promise<void> => {
    if (!loggingConfig.enabled || !getLogLevelConfig(entry.level).enabled) {
      return;
    }

    try {
      const entryKey = getLogEntryKey(entry.level, entry.timestamp, entry.id);
      const hour = Math.floor(entry.timestamp / 3600000);
      const indexKey = getLogIndexKey(entry.level, hour);

      // 准备数据
      let entryData = JSON.stringify(entry);
      const levelConfig = getLogLevelConfig(entry.level);

      // 压缩大条目
      if (loggingConfig.performance.compressionEnabled &&
        entryData.length > levelConfig.compressionThreshold) {
        const compressed = await gzip(entryData);
        entryData = compressed.toString('base64');
        entry.metadata = { ...entry.metadata, compressed: true };
      }

      const pipe = pipeline();

      // 存储日志条目
      pipe.set(entryKey, entryData);
      pipe.expire(entryKey, levelConfig.retentionDays * 24 * 3600);

      // 添加到索引
      if (loggingConfig.performance.indexingEnabled) {
        const indexEntry: LogIndexEntry = {
          timestamp: entry.timestamp,
          level: entry.level,
          module: entry.module,
          method: entry.method,
          userId: entry.userId,
          hasError: !!entry.error,
          entryKey
        };

        pipe.zadd(indexKey, entry.timestamp, JSON.stringify(indexEntry));
        pipe.expire(indexKey, levelConfig.retentionDays * 24 * 3600);
      }

      // 更新统计
      const dayKey = new Date(entry.timestamp).toISOString().split('T')[0];
      const statsKey = getLogStatsKey(entry.level, dayKey);
      pipe.hincrby(statsKey, 'total', 1);
      pipe.hincrby(statsKey, entry.module || 'unknown', 1);
      pipe.expire(statsKey, levelConfig.retentionDays * 24 * 3600);

      await executePipeline(pipe);

    } catch (error) {
      logger.error('Failed to write log entry', {
        module: MODULE_NAME,
        method: 'writeLogEntry',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error',
        details: { entryId: entry.id, level: entry.level }
      });
      throw error;
    }
  };

  /**
   * 批量写入日志条目
   */
  const writeLogEntries = async (entries: LogEntry[]): Promise<void> => {
    if (!loggingConfig.enabled || entries.length === 0) {
      return;
    }

    try {
      const pipe = pipeline();
      const processedEntries = new Map<string, number>();

      for (const entry of entries) {
        const levelConfig = getLogLevelConfig(entry.level);
        if (!levelConfig.enabled) continue;

        const entryKey = getLogEntryKey(entry.level, entry.timestamp, entry.id);
        const hour = Math.floor(entry.timestamp / 3600000);
        const indexKey = getLogIndexKey(entry.level, hour);

        // 准备数据
        let entryData = JSON.stringify(entry);

        // 压缩处理
        if (loggingConfig.performance.compressionEnabled &&
          entryData.length > levelConfig.compressionThreshold) {
          const compressed = await gzip(entryData);
          entryData = compressed.toString('base64');
          entry.metadata = { ...entry.metadata, compressed: true };
        }

        // 存储条目
        pipe.set(entryKey, entryData);
        pipe.expire(entryKey, levelConfig.retentionDays * 24 * 3600);

        // 索引处理
        if (loggingConfig.performance.indexingEnabled) {
          const indexEntry: LogIndexEntry = {
            timestamp: entry.timestamp,
            level: entry.level,
            module: entry.module,
            method: entry.method,
            userId: entry.userId,
            hasError: !!entry.error,
            entryKey
          };

          pipe.zadd(indexKey, entry.timestamp, JSON.stringify(indexEntry));
          pipe.expire(indexKey, levelConfig.retentionDays * 24 * 3600);
        }

        // 统计更新
        const dayKey = new Date(entry.timestamp).toISOString().split('T')[0];
        const statsKey = getLogStatsKey(entry.level, dayKey);
        const statsKeyStr = `${entry.level}:${dayKey}`;

        if (!processedEntries.has(statsKeyStr)) {
          processedEntries.set(statsKeyStr, 0);
        }
        processedEntries.set(statsKeyStr, processedEntries.get(statsKeyStr)! + 1);

        pipe.hincrby(statsKey, entry.module || 'unknown', 1);
        pipe.expire(statsKey, levelConfig.retentionDays * 24 * 3600);
      }

      // 批量更新总计数
      for (const [statsKeyStr, count] of processedEntries) {
        const [level, day] = statsKeyStr.split(':');
        const statsKey = getLogStatsKey(level as LogLevel, day);
        pipe.hincrby(statsKey, 'total', count);
      }

      await executePipeline(pipe);

    } catch (error) {
      logger.error('Failed to write log entries batch', {
        module: MODULE_NAME,
        method: 'writeLogEntries',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error',
        details: { entriesCount: entries.length }
      });
      throw error;
    }
  };

  /**
   * 读取日志条目
   */
  const readLogEntry = async (level: LogLevel, timestamp: number, entryId: string): Promise<LogEntry | null> => {
    try {
      const entryKey = getLogEntryKey(level, timestamp, entryId);
      const entryData = await get<string>(entryKey);

      if (!entryData) {
        return null;
      }

      let parsedData: LogEntry;

      // 检查是否压缩
      try {
        parsedData = JSON.parse(entryData);
        if (parsedData.metadata?.compressed) {
          const decompressed = await gunzip(Buffer.from(entryData, 'base64'));
          parsedData = JSON.parse(decompressed.toString());
        }
      } catch {
        // 尝试解压缩
        try {
          const decompressed = await gunzip(Buffer.from(entryData, 'base64'));
          parsedData = JSON.parse(decompressed.toString());
        } catch {
          parsedData = JSON.parse(entryData);
        }
      }

      return parsedData;
    } catch (error) {
      logger.error('Failed to read log entry', {
        module: MODULE_NAME,
        method: 'readLogEntry',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error',
        details: { level, timestamp, entryId }
      });
      return null;
    }
  };

  /**
   * 查询日志条目
   */
  const queryLogEntries = async (params: LogQueryParams): Promise<LogQueryResult> => {
    try {
      const {
        startTime = 0,
        endTime = Date.now(),
        levels = Object.values(LogLevel),
        page = 1,
        pageSize = 50,
        sortOrder = 'desc'
      } = params;

      const allEntries: LogEntry[] = [];

      // 遍历指定的日志级别
      for (const level of levels) {
        if (!getLogLevelConfig(level).enabled) continue;

        const startHour = Math.floor(startTime / 3600000);
        const endHour = Math.floor(endTime / 3600000);

        // 遍历时间范围内的小时索引
        for (let hour = startHour; hour <= endHour; hour++) {
          const indexKey = getLogIndexKey(level, hour);

          // 从有序集合中获取时间范围内的条目
          const indexEntries = sortOrder === 'desc'
            ? await zrevrange(indexKey, 0, -1, true)
            : await zrange(indexKey, 0, -1, true);

          for (let i = 0; i < indexEntries.length; i += 2) {
            const indexEntryData = indexEntries[i];
            const score = parseInt(indexEntries[i + 1]);

            if (score >= startTime && score <= endTime) {
              try {
                const indexEntry: LogIndexEntry = JSON.parse(indexEntryData);
                const logEntry = await readLogEntry(level, indexEntry.timestamp,
                  indexEntry.entryKey.split(':').pop()!);

                if (logEntry && matchesFilters(logEntry, params)) {
                  allEntries.push(logEntry);
                }
              } catch (error) {
                // 跳过损坏的条目
                continue;
              }
            }
          }
        }
      }

      // 排序
      allEntries.sort((a, b) => {
        const order = sortOrder === 'desc' ? -1 : 1;
        return order * (a.timestamp - b.timestamp);
      });

      // 分页
      const total = allEntries.length;
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const entries = allEntries.slice(startIndex, endIndex);

      return {
        entries,
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize),
        hasNext: endIndex < total,
        hasPrev: page > 1
      };

    } catch (error) {
      logger.error('Failed to query log entries', {
        module: MODULE_NAME,
        method: 'queryLogEntries',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error',
        details: params
      });
      throw error;
    }
  };

  // 辅助函数：检查日志条目是否匹配过滤条件
  const matchesFilters = (entry: LogEntry, params: LogQueryParams): boolean => {
    if (params.modules && params.modules.length > 0) {
      if (!entry.module || !params.modules.includes(entry.module)) {
        return false;
      }
    }

    if (params.methods && params.methods.length > 0) {
      if (!entry.method || !params.methods.includes(entry.method)) {
        return false;
      }
    }

    if (params.userIds && params.userIds.length > 0) {
      if (!entry.userId || !params.userIds.includes(entry.userId)) {
        return false;
      }
    }

    if (params.keyword) {
      const keyword = params.keyword.toLowerCase();
      const searchText = `${entry.message} ${entry.module || ''} ${entry.method || ''}`.toLowerCase();
      if (!searchText.includes(keyword)) {
        return false;
      }
    }

    return true;
  };

  /**
   * 获取日志统计
   */
  const getLogStats = async (params: LogStatsParams): Promise<LogStatsResult> => {
    try {
      const {
        startTime = Date.now() - 24 * 60 * 60 * 1000, // 默认24小时
        endTime = Date.now(),
        levels = Object.values(LogLevel),
        groupBy = 'hour'
      } = params;

      const stats: LogStatsResult = {
        total: 0,
        byLevel: {} as Record<LogLevel, number>,
        byModule: {},
        byHour: {},
        byDay: {},
        errorRate: 0,
        topErrors: []
      };

      // 初始化级别统计
      Object.values(LogLevel).forEach(level => {
        stats.byLevel[level] = 0;
      });

      const startDay = new Date(startTime).toISOString().split('T')[0];
      const endDay = new Date(endTime).toISOString().split('T')[0];

      // 遍历日期范围
      const currentDate = new Date(startDay);
      const endDate = new Date(endDay);

      while (currentDate <= endDate) {
        const dayKey = currentDate.toISOString().split('T')[0];

        for (const level of levels) {
          const statsKey = getLogStatsKey(level, dayKey);
          const dayStats = await hgetall<Record<string, number>>(statsKey);

          if (dayStats) {
            const total = dayStats.total || 0;
            stats.total += total;
            stats.byLevel[level] += total;

            // 按模块统计
            Object.entries(dayStats).forEach(([module, count]) => {
              if (module !== 'total') {
                stats.byModule[module] = (stats.byModule[module] || 0) + count;
              }
            });

            // 按天统计
            if (groupBy === 'day') {
              stats.byDay[dayKey] = (stats.byDay[dayKey] || 0) + total;
            }
          }
        }

        currentDate.setDate(currentDate.getDate() + 1);
      }

      // 计算错误率
      const errorCount = stats.byLevel[LogLevel.ERROR];
      stats.errorRate = stats.total > 0 ? (errorCount / stats.total) * 100 : 0;

      return stats;

    } catch (error) {
      logger.error('Failed to get log stats', {
        module: MODULE_NAME,
        method: 'getLogStats',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error',
        details: params
      });
      throw error;
    }
  };

  /**
   * 清理过期日志
   */
  const cleanupExpiredLogs = async (): Promise<LogCleanupResult> => {
    const startTime = Date.now();
    const result: LogCleanupResult = {
      deletedEntries: 0,
      deletedBytes: 0,
      processedLevels: [],
      duration: 0,
      errors: []
    };

    try {
      for (const level of Object.values(LogLevel)) {
        const levelConfig = getLogLevelConfig(level);
        if (!levelConfig.enabled) continue;

        result.processedLevels.push(level);
        const cutoffTime = Date.now() - (levelConfig.retentionDays * 24 * 60 * 60 * 1000);

        try {
          // 查找过期的索引键
          const pattern = getKey('index', level.toLowerCase(), '*');
          const indexKeys = await getAllKeys(pattern);

          for (const indexKey of indexKeys) {
            const hour = parseInt(indexKey.split(':').pop()!);
            const hourTimestamp = hour * 3600000;

            if (hourTimestamp < cutoffTime) {
              // 获取该小时的所有日志条目
              const indexEntries = await zrange(indexKey, 0, -1);

              const pipe = pipeline();
              let deletedInThisHour = 0;

              for (const indexEntryData of indexEntries) {
                try {
                  const indexEntry: LogIndexEntry = JSON.parse(indexEntryData);
                  pipe.del(indexEntry.entryKey);
                  deletedInThisHour++;
                } catch (error) {
                  result.errors.push(`Failed to parse index entry: ${error}`);
                }
              }

              // 删除索引键
              pipe.del(indexKey);

              await executePipeline(pipe);
              result.deletedEntries += deletedInThisHour;
            }
          }

          // 清理过期的统计数据
          const statsPattern = getKey('stats', level.toLowerCase(), '*');
          const statsKeys = await getAllKeys(statsPattern);

          for (const statsKey of statsKeys) {
            const dayStr = statsKey.split(':').pop()!;
            const dayTime = new Date(dayStr).getTime();

            if (dayTime < cutoffTime) {
              await del(statsKey);
            }
          }

        } catch (error) {
          const errorMsg = `Failed to cleanup ${level} logs: ${error}`;
          result.errors.push(errorMsg);
          logger.error(errorMsg, {
            module: MODULE_NAME,
            method: 'cleanupExpiredLogs',
            timestamp: getTimestamp()
          });
        }
      }

      result.duration = Date.now() - startTime;

      logger.info('Log cleanup completed', {
        module: MODULE_NAME,
        method: 'cleanupExpiredLogs',
        timestamp: getTimestamp(),
        details: result
      });

      return result;

    } catch (error) {
      result.duration = Date.now() - startTime;
      result.errors.push(`Cleanup failed: ${error}`);

      logger.error('Log cleanup failed', {
        module: MODULE_NAME,
        method: 'cleanupExpiredLogs',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      return result;
    }
  };

  /**
   * 获取日志健康状态
   */
  const getLogHealth = async () => {
    try {
      const health = {
        status: 'healthy' as const,
        totalEntries: 0,
        entriesByLevel: {} as Record<LogLevel, number>,
        oldestEntry: Date.now(),
        newestEntry: 0,
        storageUsage: {
          totalBytes: 0,
          byLevel: {} as Record<LogLevel, number>
        },
        issues: [] as string[],
        recommendations: [] as string[]
      };

      // 获取各级别的统计信息
      for (const level of Object.values(LogLevel)) {
        const levelConfig = getLogLevelConfig(level);
        if (!levelConfig.enabled) continue;

        // 获取最近的统计数据
        const today = new Date().toISOString().split('T')[0];
        const statsKey = getLogStatsKey(level, today);
        const stats = await hgetall<Record<string, number>>(statsKey);

        const levelTotal = stats?.total || 0;
        health.totalEntries += levelTotal;
        health.entriesByLevel[level] = levelTotal;

        // 检查是否超过每小时限制
        if (levelTotal > levelConfig.maxEntriesPerHour) {
          health.issues.push(`${level} logs exceed hourly limit (${levelTotal}/${levelConfig.maxEntriesPerHour})`);
          health.status = 'warning';
        }
      }

      // 添加建议
      if (health.totalEntries === 0) {
        health.recommendations.push('No log entries found, check if logging is properly configured');
      }

      if (health.issues.length > 3) {
        health.status = 'error';
        health.recommendations.push('Multiple issues detected, consider reviewing log configuration');
      }

      return health;

    } catch (error) {
      logger.error('Failed to get log health', {
        module: MODULE_NAME,
        method: 'getLogHealth',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  };

  return {
    writeLogEntry,
    writeLogEntries,
    readLogEntry,
    queryLogEntries,
    getLogStats,
    cleanupExpiredLogs,
    getLogHealth
  };
};

export const logService = createLogService();
export default logService;
