import { createRedisService } from '../base.js';
import type { StatsTimestamps } from '@/types/stats.js';

const NAMESPACE = 'stats';
const STATS_TTL = 7 * 24 * 60 * 60; // 7天

const createStatsService = () => {
  const redisService = createRedisService(NAMESPACE);
  const { getKey, hset, hget, hgetall, hdel, exists, expire, del, scan } = redisService;

  // GET 请求统计键
  const getGetStatsKey = (timestamp: number): string => getKey('get', timestamp);

  // POST 请求统计键
  const getPostStatsKey = (timestamp: number): string => getKey('post', timestamp);

  /**
   * 记录 GET 请求统计
   */
  const recordGetRequest = async (path: string, timestamp?: number): Promise<void> => {
    const ts = timestamp || Math.floor(Date.now() / 1000);
    const key = getGetStatsKey(ts);

    // 获取当前计数
    const currentCount = await hget<number>(key, path) || 0;

    // 增加计数
    await hset(key, path, currentCount + 1);
    await expire(key, STATS_TTL);
  };

  /**
   * 记录 POST 请求统计
   */
  const recordPostRequest = async (path: string, timestamp?: number): Promise<void> => {
    const ts = timestamp || Math.floor(Date.now() / 1000);
    const key = getPostStatsKey(ts);

    // 获取当前计数
    const currentCount = await hget<number>(key, path) || 0;

    // 增加计数
    await hset(key, path, currentCount + 1);
    await expire(key, STATS_TTL);
  };

  /**
   * 批量记录 GET 请求统计
   */
  const recordGetRequests = async (requests: Array<{ path: string; timestamp?: number }>): Promise<void> => {
    if (requests.length === 0) return;

    const pipeline = redisService.pipeline();
    const timestampKeys = new Set<string>();

    for (const request of requests) {
      const ts = request.timestamp || Math.floor(Date.now() / 1000);
      const key = getGetStatsKey(ts);
      timestampKeys.add(key);

      // 先获取当前值，然后增加
      const currentCount = await hget<number>(key, request.path) || 0;
      pipeline.hset(key, request.path, currentCount + 1);
    }

    // 设置过期时间
    for (const key of timestampKeys) {
      pipeline.expire(key, STATS_TTL);
    }

    await redisService.executePipeline(pipeline);
  };

  /**
   * 批量记录 POST 请求统计
   */
  const recordPostRequests = async (requests: Array<{ path: string; timestamp?: number }>): Promise<void> => {
    if (requests.length === 0) return;

    const pipeline = redisService.pipeline();
    const timestampKeys = new Set<string>();

    for (const request of requests) {
      const ts = request.timestamp || Math.floor(Date.now() / 1000);
      const key = getPostStatsKey(ts);
      timestampKeys.add(key);

      // 先获取当前值，然后增加
      const currentCount = await hget<number>(key, request.path) || 0;
      pipeline.hset(key, request.path, currentCount + 1);
    }

    // 设置过期时间
    for (const key of timestampKeys) {
      pipeline.expire(key, STATS_TTL);
    }

    await redisService.executePipeline(pipeline);
  };

  /**
   * 获取 GET 请求统计
   */
  const getGetStats = async (timestamp: number): Promise<Map<string, number>> => {
    const key = getGetStatsKey(timestamp);
    const stats = await hgetall<number>(key);

    const result = new Map<string, number>();
    for (const [path, count] of Object.entries(stats)) {
      result.set(path, count);
    }
    return result;
  };

  /**
   * 获取 POST 请求统计
   */
  const getPostStats = async (timestamp: number): Promise<Map<string, number>> => {
    const key = getPostStatsKey(timestamp);
    const stats = await hgetall<number>(key);

    const result = new Map<string, number>();
    for (const [path, count] of Object.entries(stats)) {
      result.set(path, count);
    }
    return result;
  };

  /**
   * 获取指定路径的 GET 请求统计
   */
  const getGetStatsForPath = async (path: string, timestamp: number): Promise<number> => {
    const key = getGetStatsKey(timestamp);
    return await hget<number>(key, path) || 0;
  };

  /**
   * 获取指定路径的 POST 请求统计
   */
  const getPostStatsForPath = async (path: string, timestamp: number): Promise<number> => {
    const key = getPostStatsKey(timestamp);
    return await hget<number>(key, path) || 0;
  };

  /**
   * 获取时间范围内的 GET 请求统计
   */
  const getGetStatsRange = async (startTimestamp: number, endTimestamp: number): Promise<Map<number, Map<string, number>>> => {
    const result = new Map<number, Map<string, number>>();

    for (let ts = startTimestamp; ts <= endTimestamp; ts++) {
      const stats = await getGetStats(ts);
      if (stats.size > 0) {
        result.set(ts, stats);
      }
    }

    return result;
  };

  /**
   * 获取时间范围内的 POST 请求统计
   */
  const getPostStatsRange = async (startTimestamp: number, endTimestamp: number): Promise<Map<number, Map<string, number>>> => {
    const result = new Map<number, Map<string, number>>();

    for (let ts = startTimestamp; ts <= endTimestamp; ts++) {
      const stats = await getPostStats(ts);
      if (stats.size > 0) {
        result.set(ts, stats);
      }
    }

    return result;
  };

  /**
   * 删除指定时间戳的统计数据
   */
  const deleteStats = async (timestamp: number): Promise<boolean> => {
    const getKey = getGetStatsKey(timestamp);
    const postKey = getPostStatsKey(timestamp);

    const results = await Promise.all([
      del(getKey),
      del(postKey)
    ]);

    return results.some(result => result > 0);
  };

  /**
   * 清理过期的统计数据
   */
  const cleanupExpiredStats = async (): Promise<number> => {
    const now = Math.floor(Date.now() / 1000);
    const expiredThreshold = now - STATS_TTL;

    let deletedCount = 0;
    let cursor = 0;

    do {
      const [nextCursor, keys] = await scan(cursor, 'get:*', 100);
      cursor = parseInt(nextCursor, 10);

      for (const key of keys) {
        const parts = key.split(':');
        const timestamp = parseInt(parts[parts.length - 1], 10);

        if (!isNaN(timestamp) && timestamp < expiredThreshold) {
          await del(key);
          deletedCount++;
        }
      }
    } while (cursor !== 0);

    // 清理 POST 统计
    cursor = 0;
    do {
      const [nextCursor, keys] = await scan(cursor, 'post:*', 100);
      cursor = parseInt(nextCursor, 10);

      for (const key of keys) {
        const parts = key.split(':');
        const timestamp = parseInt(parts[parts.length - 1], 10);

        if (!isNaN(timestamp) && timestamp < expiredThreshold) {
          await del(key);
          deletedCount++;
        }
      }
    } while (cursor !== 0);

    return deletedCount;
  };

  /**
   * 获取所有统计时间戳
   * @returns 包含 GET 和 POST 请求时间戳列表的对象
   */
  const getAllStatsTimestamps = async (): Promise<StatsTimestamps> => {
    const getTimestamps: number[] = [];
    const postTimestamps: number[] = [];

    // 获取 GET 统计时间戳
    let cursor = 0;
    do {
      const [nextCursor, keys] = await scan(cursor, 'get:*', 100);
      cursor = parseInt(nextCursor, 10);

      for (const key of keys) {
        const parts = key.split(':');
        const timestamp = parseInt(parts[parts.length - 1], 10);
        if (!isNaN(timestamp)) {
          getTimestamps.push(timestamp);
        }
      }
    } while (cursor !== 0);

    // 获取 POST 统计时间戳
    cursor = 0;
    do {
      const [nextCursor, keys] = await scan(cursor, 'post:*', 100);
      cursor = parseInt(nextCursor, 10);

      for (const key of keys) {
        const parts = key.split(':');
        const timestamp = parseInt(parts[parts.length - 1], 10);
        if (!isNaN(timestamp)) {
          postTimestamps.push(timestamp);
        }
      }
    } while (cursor !== 0);

    return {
      get: getTimestamps.sort((a, b) => a - b),
      post: postTimestamps.sort((a, b) => a - b)
    };
  };

  return {
    recordGetRequest,
    recordPostRequest,
    recordGetRequests,
    recordPostRequests,
    getGetStats,
    getPostStats,
    getGetStatsForPath,
    getPostStatsForPath,
    getGetStatsRange,
    getPostStatsRange,
    deleteStats,
    cleanupExpiredStats,
    getAllStatsTimestamps,
  };
};

// 创建单例实例
const statsService = createStatsService();

export const {
  recordGetRequest,
  recordPostRequest,
  recordGetRequests,
  recordPostRequests,
  getGetStats,
  getPostStats,
  getGetStatsForPath,
  getPostStatsForPath,
  getGetStatsRange,
  getPostStatsRange,
  deleteStats,
  cleanupExpiredStats,
  getAllStatsTimestamps,
} = statsService;

export default statsService;
