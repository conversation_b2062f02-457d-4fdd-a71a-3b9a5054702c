import { createRedisService } from '../base.js';
import type { RoomWithStatus } from '@/types/index.js';
import { RoomActiveStatus } from '@unibabble/shared';

const NAMESPACE = 'rooms';
const ROOM_TTL = 24 * 60 * 60; // 24小时

const createRoomService = () => {
  const redisService = createRedisService(NAMESPACE);
  const { getKey, set, get, del, exists, expire, hset, hget, hmset, hgetall, hdel, sadd, srem, smembers, sismember } = redisService;

  // 房间基本信息键
  const getRoomKey = (roomId: number): string => getKey('info', roomId);
  
  // 房间用户列表键
  const getRoomUsersKey = (roomId: number): string => getKey('users', roomId);
  
  // 被踢用户集合键
  const getRoomKickedKey = (roomId: number): string => getKey('kicked', roomId);

  /**
   * 创建或更新房间
   */
  const setRoom = async (room: RoomWithStatus): Promise<void> => {
    const roomKey = getRoomKey(room.id);
    const usersKey = getRoomUsersKey(room.id);
    const kickedKey = getRoomKickedKey(room.id);

    // 存储房间基本信息（不包括用户列表和被踢用户）
    const roomData = {
      id: room.id,
      adminId: room.adminId,
      createdAt: room.createdAt,
      status: room.status,
      lastActiveAt: room.lastActiveAt,
      messageCounter: room.messageCounter,
      adminToken: room.adminToken,
      ackCount: room.ackCount || 0
    };

    await set(roomKey, roomData);
    await expire(roomKey, ROOM_TTL);

    // 存储用户列表
    if (room.users && room.users.length > 0) {
      await del(usersKey); // 清空现有用户列表
      for (const user of room.users) {
        await hset(usersKey, user.id, user);
      }
      await expire(usersKey, ROOM_TTL);
    }

    // 存储被踢用户集合
    if (room.kickedIds && room.kickedIds.size > 0) {
      await del(kickedKey); // 清空现有被踢用户
      await sadd(kickedKey, ...Array.from(room.kickedIds));
      await expire(kickedKey, ROOM_TTL);
    }
  };

  /**
   * 获取房间信息
   */
  const getRoom = async (roomId: number): Promise<RoomWithStatus | null> => {
    const roomKey = getRoomKey(roomId);
    const usersKey = getRoomUsersKey(roomId);
    const kickedKey = getRoomKickedKey(roomId);

    const roomData = await get<any>(roomKey);
    if (!roomData) return null;

    // 获取用户列表
    const usersData = await hgetall<any>(usersKey);
    const users = Object.values(usersData);

    // 获取被踢用户集合
    const kickedUsers = await smembers(kickedKey);
    const kickedIds = new Set(kickedUsers);

    return {
      ...roomData,
      users,
      kickedIds,
      status: roomData.status as RoomActiveStatus
    } as RoomWithStatus;
  };

  /**
   * 删除房间
   */
  const deleteRoom = async (roomId: number): Promise<boolean> => {
    const roomKey = getRoomKey(roomId);
    const usersKey = getRoomUsersKey(roomId);
    const kickedKey = getRoomKickedKey(roomId);

    const results = await Promise.all([
      del(roomKey),
      del(usersKey),
      del(kickedKey)
    ]);

    return results.some(result => result > 0);
  };

  /**
   * 检查房间是否存在
   */
  const roomExists = async (roomId: number): Promise<boolean> => {
    const roomKey = getRoomKey(roomId);
    return exists(roomKey);
  };

  /**
   * 添加用户到房间
   */
  const addUserToRoom = async (roomId: number, user: any): Promise<void> => {
    const usersKey = getRoomUsersKey(roomId);
    await hset(usersKey, user.id, user);
    await expire(usersKey, ROOM_TTL);
  };

  /**
   * 从房间移除用户
   */
  const removeUserFromRoom = async (roomId: number, userId: string): Promise<void> => {
    const usersKey = getRoomUsersKey(roomId);
    await hdel(usersKey, userId);
  };

  /**
   * 获取房间用户列表
   */
  const getRoomUsers = async (roomId: number): Promise<any[]> => {
    const usersKey = getRoomUsersKey(roomId);
    const usersData = await hgetall<any>(usersKey);
    return Object.values(usersData);
  };

  /**
   * 添加用户到被踢列表
   */
  const addKickedUser = async (roomId: number, userId: string): Promise<void> => {
    const kickedKey = getRoomKickedKey(roomId);
    await sadd(kickedKey, userId);
    await expire(kickedKey, ROOM_TTL);
  };

  /**
   * 从被踢列表移除用户
   */
  const removeKickedUser = async (roomId: number, userId: string): Promise<void> => {
    const kickedKey = getRoomKickedKey(roomId);
    await srem(kickedKey, userId);
  };

  /**
   * 检查用户是否被踢
   */
  const isUserKicked = async (roomId: number, userId: string): Promise<boolean> => {
    const kickedKey = getRoomKickedKey(roomId);
    return sismember(kickedKey, userId);
  };

  /**
   * 更新房间最后活跃时间
   */
  const updateLastActiveTime = async (roomId: number, timestamp: number): Promise<void> => {
    const roomKey = getRoomKey(roomId);
    await hset(roomKey, 'lastActiveAt', timestamp);
    await expire(roomKey, ROOM_TTL);
  };

  /**
   * 更新房间状态
   */
  const updateRoomStatus = async (roomId: number, status: RoomActiveStatus): Promise<void> => {
    const roomKey = getRoomKey(roomId);
    await hset(roomKey, 'status', status);
    await expire(roomKey, ROOM_TTL);
  };

  /**
   * 增加消息计数器
   */
  const incrementMessageCounter = async (roomId: number): Promise<number> => {
    const roomKey = getRoomKey(roomId);
    const room = await get<any>(roomKey);
    if (!room) return 0;
    
    const newCount = (room.messageCounter || 0) + 1;
    await hset(roomKey, 'messageCounter', newCount);
    await expire(roomKey, ROOM_TTL);
    return newCount;
  };

  /**
   * 获取所有房间ID（用于清理任务）
   */
  const getAllRoomIds = async (): Promise<number[]> => {
    const pattern = getKey('info', '*');
    const keys = await redisService.getAllKeys('info:*');
    return keys.map(key => {
      const parts = key.split(':');
      return parseInt(parts[parts.length - 1], 10);
    }).filter(id => !isNaN(id));
  };

  return {
    setRoom,
    getRoom,
    deleteRoom,
    roomExists,
    addUserToRoom,
    removeUserFromRoom,
    getRoomUsers,
    addKickedUser,
    removeKickedUser,
    isUserKicked,
    updateLastActiveTime,
    updateRoomStatus,
    incrementMessageCounter,
    getAllRoomIds,
  };
};

// 创建单例实例
const roomService = createRoomService();

export const {
  setRoom,
  getRoom,
  deleteRoom,
  roomExists,
  addUserToRoom,
  removeUserFromRoom,
  getRoomUsers,
  addKickedUser,
  removeKickedUser,
  isUserKicked,
  updateLastActiveTime,
  updateRoomStatus,
  incrementMessageCounter,
  getAllRoomIds,
} = roomService;

export default roomService;
