import { createRedisService } from '../base.js';
import type { UserRoomState, UserWithStatus } from '@/types/index.js';
import type { User } from '@unibabble/shared';

const NAMESPACE = 'users';
const USER_TTL = 24 * 60 * 60; // 24小时
const USER_ROOM_STATE_TTL = 6 * 60 * 60; // 6小时

const createUserService = () => {
  const redisService = createRedisService(NAMESPACE);
  const { getKey, set, get, del, exists, expire, hset, hget, hmset, hgetall, hdel, hkeys } = redisService;

  // 用户基本信息键
  const getUserKey = (userId: string): string => getKey('info', userId);
  
  // 用户房间状态键
  const getUserRoomStateKey = (userId: string): string => getKey('rooms', userId);

  /**
   * 设置用户信息
   */
  const setUser = async (user: UserWithStatus): Promise<void> => {
    const userKey = getUserKey(user.id);
    await set(userKey, user);
    await expire(userKey, USER_TTL);
  };

  /**
   * 获取用户信息
   */
  const getUser = async (userId: string): Promise<User | null> => {
    const userKey = getUserKey(userId);
    return get<User>(userKey);
  };

  /**
   * 获取带状态的用户信息
   */
  const getUserWithStatus = async (userId: string): Promise<UserWithStatus | null> => {
    const userKey = getUserKey(userId);
    return get<UserWithStatus>(userKey);
  };

  /**
   * 删除用户
   */
  const deleteUser = async (userId: string): Promise<boolean> => {
    const userKey = getUserKey(userId);
    const userRoomStateKey = getUserRoomStateKey(userId);
    
    const results = await Promise.all([
      del(userKey),
      del(userRoomStateKey)
    ]);

    return results.some(result => result > 0);
  };

  /**
   * 检查用户是否存在
   */
  const userExists = async (userId: string): Promise<boolean> => {
    const userKey = getUserKey(userId);
    return exists(userKey);
  };

  /**
   * 设置用户在房间的状态
   */
  const setUserRoomState = async (userId: string, roomId: number, state: UserRoomState): Promise<void> => {
    const userRoomStateKey = getUserRoomStateKey(userId);
    await hset(userRoomStateKey, roomId.toString(), state);
    await expire(userRoomStateKey, USER_ROOM_STATE_TTL);
  };

  /**
   * 获取用户在房间的状态
   */
  const getUserRoomState = async (userId: string, roomId: number): Promise<UserRoomState | null> => {
    const userRoomStateKey = getUserRoomStateKey(userId);
    return hget<UserRoomState>(userRoomStateKey, roomId.toString());
  };

  /**
   * 获取用户所有房间状态
   */
  const getAllUserRoomStates = async (userId: string): Promise<Map<number, UserRoomState>> => {
    const userRoomStateKey = getUserRoomStateKey(userId);
    const states = await hgetall<UserRoomState>(userRoomStateKey);
    
    const result = new Map<number, UserRoomState>();
    for (const [roomIdStr, state] of Object.entries(states)) {
      const roomId = parseInt(roomIdStr, 10);
      if (!isNaN(roomId)) {
        result.set(roomId, state);
      }
    }
    return result;
  };

  /**
   * 删除用户在房间的状态
   */
  const deleteUserRoomState = async (userId: string, roomId: number): Promise<void> => {
    const userRoomStateKey = getUserRoomStateKey(userId);
    await hdel(userRoomStateKey, roomId.toString());
  };

  /**
   * 获取用户参与的所有房间ID
   */
  const getUserRoomIds = async (userId: string): Promise<number[]> => {
    const userRoomStateKey = getUserRoomStateKey(userId);
    const roomIdStrs = await hkeys(userRoomStateKey);
    return roomIdStrs.map(str => parseInt(str, 10)).filter(id => !isNaN(id));
  };

  /**
   * 更新用户消息计数
   */
  const updateUserMessageCount = async (userId: string, roomId: number, increment: number = 1): Promise<number> => {
    const state = await getUserRoomState(userId, roomId);
    if (!state) return 0;

    const newCount = (state.msgCount || 0) + increment;
    const updatedState = {
      ...state,
      msgCount: newCount,
      timestamp: Date.now()
    };

    await setUserRoomState(userId, roomId, updatedState);
    return newCount;
  };

  /**
   * 更新用户房间状态时间戳
   */
  const updateUserRoomTimestamp = async (userId: string, roomId: number, timestamp?: number): Promise<void> => {
    const state = await getUserRoomState(userId, roomId);
    if (!state) return;

    const updatedState = {
      ...state,
      timestamp: timestamp || Date.now()
    };

    await setUserRoomState(userId, roomId, updatedState);
  };

  /**
   * 批量获取用户信息
   */
  const getUsers = async (userIds: string[]): Promise<Map<string, User>> => {
    if (userIds.length === 0) return new Map();

    const keys = userIds.map(id => getUserKey(id));
    const users = await redisService.mget<User>(keys);
    
    const result = new Map<string, User>();
    for (let i = 0; i < userIds.length; i++) {
      const user = users[i];
      if (user) {
        result.set(userIds[i], user);
      }
    }
    return result;
  };

  /**
   * 批量设置用户信息
   */
  const setUsers = async (users: UserWithStatus[]): Promise<void> => {
    if (users.length === 0) return;

    const keyValuePairs: Record<string, UserWithStatus> = {};
    for (const user of users) {
      const key = getUserKey(user.id);
      keyValuePairs[key] = user;
    }

    await redisService.mset(keyValuePairs);

    // 设置过期时间
    const pipeline = redisService.pipeline();
    for (const user of users) {
      const key = getUserKey(user.id);
      pipeline.expire(key, USER_TTL);
    }
    await redisService.executePipeline(pipeline);
  };

  /**
   * 获取所有用户ID（用于清理任务）
   */
  const getAllUserIds = async (): Promise<string[]> => {
    const keys = await redisService.getAllKeys('info:*');
    return keys.map(key => {
      const parts = key.split(':');
      return parts[parts.length - 1];
    }).filter(id => id && id !== '*');
  };

  /**
   * 清理过期的用户房间状态
   */
  const cleanupExpiredUserRoomStates = async (userId: string): Promise<number> => {
    const states = await getAllUserRoomStates(userId);
    const now = Date.now();
    const expiredThreshold = now - USER_ROOM_STATE_TTL * 1000;
    
    let cleanedCount = 0;
    for (const [roomId, state] of states) {
      if (state.timestamp < expiredThreshold) {
        await deleteUserRoomState(userId, roomId);
        cleanedCount++;
      }
    }
    
    return cleanedCount;
  };

  return {
    setUser,
    getUser,
    getUserWithStatus,
    deleteUser,
    userExists,
    setUserRoomState,
    getUserRoomState,
    getAllUserRoomStates,
    deleteUserRoomState,
    getUserRoomIds,
    updateUserMessageCount,
    updateUserRoomTimestamp,
    getUsers,
    setUsers,
    getAllUserIds,
    cleanupExpiredUserRoomStates,
  };
};

// 创建单例实例
const userService = createUserService();

export const {
  setUser,
  getUser,
  getUserWithStatus,
  deleteUser,
  userExists,
  setUserRoomState,
  getUserRoomState,
  getAllUserRoomStates,
  deleteUserRoomState,
  getUserRoomIds,
  updateUserMessageCount,
  updateUserRoomTimestamp,
  getUsers,
  setUsers,
  getAllUserIds,
  cleanupExpiredUserRoomStates,
} = userService;

export default userService;
