import { createRedisService } from '../base.js';
import type { UserInviteStatus } from './types.js';
import { RANDOM_USER_TTL } from './types.js';

const NAMESPACE = 'random-users';

const createRandomUserService = () => {
  const redisService = createRedisService(NAMESPACE);
  const { getKey, setWithExpiry, getParsed, redis } = redisService;

  const setUserStatus = async (
    sessionId: string,
    status: Omit<UserInviteStatus, 'expiresAt'>
  ): Promise<void> => {
    const key = getKey(sessionId);
    const userStatus: UserInviteStatus = {
      ...status,
      expiresAt: Date.now() + RANDOM_USER_TTL * 1000,
    };
    
    await setWithExpiry(
      key,
      userStatus,
      RANDOM_USER_TTL
    );
  };

  const getUserStatus = async (sessionId: string): Promise<UserInviteStatus | null> => {
    const key = getKey(sessionId);
    return getParsed<UserInviteStatus>(key);
  };

  const updateUserStatus = async (
    sessionId: string,
    updates: Partial<Omit<UserInviteStatus, 'userId'>>
  ): Promise<boolean> => {
    const existing = await getUserStatus(sessionId);
    if (!existing) return false;

    const updatedStatus = { ...existing, ...updates };
    const key = getKey(sessionId);
    
    // 计算剩余的TTL
    const ttl = Math.ceil((updatedStatus.expiresAt - Date.now()) / 1000);
    if (ttl <= 0) return false;

    await setWithExpiry(key, updatedStatus, ttl);
    return true;
  };

  const deleteUserStatus = async (sessionId: string): Promise<boolean> => {
    const key = getKey(sessionId);
    const result = await redis.del(key);
    return result > 0;
  };

  const cleanupExpiredSessions = async (): Promise<number> => {
    const pattern = getKey('*');
    const keys = await redis.keys(pattern);
    
    let deletedCount = 0;
    const now = Date.now();
    
    for (const key of keys) {
      const status = await getParsed<UserInviteStatus>(key);
      if (status && status.expiresAt <= now) {
        await redis.del(key);
        deletedCount++;
      }
    }
    
    return deletedCount;
  };

  return {
    setUserStatus,
    getUserStatus,
    updateUserStatus,
    deleteUserStatus,
    cleanupExpiredSessions,
  };
};

// 创建单例实例
const randomUserService = createRandomUserService();

export const {
  setUserStatus,
  getUserStatus,
  updateUserStatus,
  deleteUserStatus,
  cleanupExpiredSessions,
} = randomUserService;

export default randomUserService;
