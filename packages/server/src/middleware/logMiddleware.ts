import type { Request, Response, NextFunction } from 'express';
import { logManager } from '@/managers/logManager.js';
import { LogLevel } from '@/configs/logging.js';
import { logger, getTimestamp } from '@unibabble/shared';
import { v4 as uuidv4 } from 'uuid';

const MODULE_NAME = 'Server:middleware:log';

/**
 * 扩展 Request 接口以包含日志相关信息
 */
declare global {
  namespace Express {
    interface Request {
      requestId?: string;
      startTime?: number;
      logContext?: {
        userId?: string;
        sessionId?: string;
        module?: string;
        method?: string;
      };
    }
  }
}

/**
 * 请求日志中间件
 * 自动记录 HTTP 请求的详细信息
 */
export const requestLogMiddleware = (req: Request, res: Response, next: NextFunction) => {
  // 生成请求ID和记录开始时间
  req.requestId = uuidv4();
  req.startTime = Date.now();

  // 设置日志上下文
  req.logContext = {
    userId: req.headers['x-user-id'] as string,
    sessionId: req.headers['x-session-id'] as string,
    module: 'HTTP',
    method: req.method
  };

  // 记录请求开始
  const logRequestStart = async () => {
    try {
      await logManager.writeLog({
        level: LogLevel.INFO,
        message: `HTTP Request Started: ${req.method} ${req.path}`,
        module: MODULE_NAME,
        method: 'requestStart',
        requestId: req.requestId,
        userId: req.logContext?.userId,
        sessionId: req.logContext?.sessionId,
        context: {
          ip: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          path: req.path,
          method: req.method
        },
        metadata: {
          headers: filterSensitiveHeaders(req.headers),
          query: req.query,
          params: req.params
        }
      });
    } catch (error) {
      logger.error('Failed to log request start', {
        module: MODULE_NAME,
        method: 'requestStart',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  // 异步记录请求开始，不阻塞请求
  logRequestStart();

  // 监听响应完成
  res.on('finish', () => {
    const duration = Date.now() - (req.startTime || Date.now());
    const statusCode = res.statusCode;

    const logRequestEnd = async () => {
      try {
        // 根据状态码确定日志级别
        let level = LogLevel.INFO;
        if (statusCode >= 400 && statusCode < 500) {
          level = LogLevel.WARN;
        } else if (statusCode >= 500) {
          level = LogLevel.ERROR;
        }

        await logManager.writeLog({
          level,
          message: `HTTP Request Completed: ${req.method} ${req.path} - ${statusCode} (${duration}ms)`,
          module: MODULE_NAME,
          method: 'requestEnd',
          requestId: req.requestId,
          userId: req.logContext?.userId,
          sessionId: req.logContext?.sessionId,
          context: {
            ip: req.ip || req.connection.remoteAddress,
            userAgent: req.headers['user-agent'],
            path: req.path,
            method: req.method,
            statusCode
          },
          performance: {
            duration,
            memoryUsage: process.memoryUsage()
          },
          metadata: {
            responseSize: res.get('content-length'),
            cacheHit: res.get('x-cache-hit') === 'true'
          }
        });

        // 记录慢请求
        if (duration > 5000) { // 5秒
          await logManager.writeLog({
            level: LogLevel.WARN,
            message: `Slow HTTP Request: ${req.method} ${req.path} took ${duration}ms`,
            module: MODULE_NAME,
            method: 'slowRequest',
            requestId: req.requestId,
            userId: req.logContext?.userId,
            sessionId: req.logContext?.sessionId,
            performance: {
              duration,
              memoryUsage: process.memoryUsage(),
              cpuUsage: process.cpuUsage()
            },
            metadata: {
              threshold: 5000,
              actualDuration: duration
            }
          });
        }

      } catch (error) {
        logger.error('Failed to log request end', {
          module: MODULE_NAME,
          method: 'requestEnd',
          timestamp: getTimestamp(),
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    };

    // 异步记录请求结束
    logRequestEnd();
  });

  // 监听响应错误
  res.on('error', (error: Error) => {
    const logRequestError = async () => {
      try {
        await logManager.writeLog({
          level: LogLevel.ERROR,
          message: `HTTP Request Error: ${req.method} ${req.path}`,
          module: MODULE_NAME,
          method: 'requestError',
          requestId: req.requestId,
          userId: req.logContext?.userId,
          sessionId: req.logContext?.sessionId,
          error: {
            name: error.name,
            message: error.message,
            stack: error.stack
          },
          context: {
            ip: req.ip || req.connection.remoteAddress,
            userAgent: req.headers['user-agent'],
            path: req.path,
            method: req.method
          }
        });
      } catch (logError) {
        logger.error('Failed to log request error', {
          module: MODULE_NAME,
          method: 'requestError',
          timestamp: getTimestamp(),
          error: logError instanceof Error ? logError.message : 'Unknown error'
        });
      }
    };

    logRequestError();
  });

  next();
};

/**
 * 错误日志中间件
 * 捕获和记录应用程序错误
 */
export const errorLogMiddleware = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const logError = async () => {
    try {
      await logManager.writeLog({
        level: LogLevel.ERROR,
        message: `Application Error: ${error.message}`,
        module: MODULE_NAME,
        method: 'applicationError',
        requestId: req.requestId,
        userId: req.logContext?.userId,
        sessionId: req.logContext?.sessionId,
        error: {
          name: error.name,
          message: error.message,
          stack: error.stack
        },
        context: {
          ip: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          path: req.path,
          method: req.method
        },
        metadata: {
          errorType: 'application_error',
          timestamp: Date.now()
        }
      });
    } catch (logError) {
      logger.error('Failed to log application error', {
        module: MODULE_NAME,
        method: 'applicationError',
        timestamp: getTimestamp(),
        error: logError instanceof Error ? logError.message : 'Unknown error'
      });
    }
  };

  // 异步记录错误
  logError();

  // 继续错误处理链
  next(error);
};

/**
 * 业务日志中间件工厂
 * 为特定模块创建日志中间件
 */
export const createModuleLogMiddleware = (moduleName: string) => {
  return (req: Request, res: Response, next: NextFunction) => {
    // 更新日志上下文
    if (req.logContext) {
      req.logContext.module = moduleName;
    }

    // 记录模块访问
    const logModuleAccess = async () => {
      try {
        await logManager.writeLog({
          level: LogLevel.DEBUG,
          message: `Module Access: ${moduleName} - ${req.method} ${req.path}`,
          module: moduleName,
          method: 'moduleAccess',
          requestId: req.requestId,
          userId: req.logContext?.userId,
          sessionId: req.logContext?.sessionId,
          metadata: {
            accessTime: Date.now(),
            endpoint: req.path
          }
        });
      } catch (error) {
        logger.error('Failed to log module access', {
          module: MODULE_NAME,
          method: 'moduleAccess',
          timestamp: getTimestamp(),
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    };

    logModuleAccess();
    next();
  };
};

/**
 * 安全日志中间件
 * 记录安全相关事件
 */
export const securityLogMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const logSecurityEvent = async (eventType: string, details: Record<string, any>) => {
    try {
      await logManager.writeLog({
        level: LogLevel.WARN,
        message: `Security Event: ${eventType}`,
        module: 'Security',
        method: 'securityEvent',
        requestId: req.requestId,
        userId: req.logContext?.userId,
        sessionId: req.logContext?.sessionId,
        context: {
          ip: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          path: req.path,
          method: req.method
        },
        metadata: {
          eventType,
          ...details,
          timestamp: Date.now()
        }
      });
    } catch (error) {
      logger.error('Failed to log security event', {
        module: MODULE_NAME,
        method: 'securityEvent',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  // 检查可疑的请求头
  const suspiciousHeaders = ['x-forwarded-for', 'x-real-ip', 'x-cluster-client-ip'];
  for (const header of suspiciousHeaders) {
    if (req.headers[header]) {
      logSecurityEvent('suspicious_header', {
        header,
        value: req.headers[header]
      });
    }
  }

  // 检查大型请求体
  const contentLength = parseInt(req.headers['content-length'] || '0');
  if (contentLength > 10 * 1024 * 1024) { // 10MB
    logSecurityEvent('large_request_body', {
      contentLength,
      threshold: 10 * 1024 * 1024
    });
  }

  next();
};

/**
 * 过滤敏感请求头
 */
const filterSensitiveHeaders = (headers: Record<string, any>): Record<string, any> => {
  const sensitiveHeaders = ['authorization', 'cookie', 'x-api-key', 'x-auth-token'];
  const filtered = { ...headers };

  for (const header of sensitiveHeaders) {
    if (filtered[header]) {
      filtered[header] = '[REDACTED]';
    }
  }

  return filtered;
};

export default {
  requestLogMiddleware,
  errorLogMiddleware,
  createModuleLogMiddleware,
  securityLogMiddleware
};
