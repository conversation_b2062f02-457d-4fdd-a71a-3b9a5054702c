import type { Request, Response, NextFunction } from 'express';
import { statsManager } from '@/managers/statsManager.js';
import { logger, getTimestamp } from '@unibabble/shared';

const MODULE_NAME = 'Server:middleware:stats';

/**
 * 统计中间件
 * 自动记录 API 访问统计
 */
export const statsMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now();
  const timestamp = Math.floor(startTime / 1000);
  const path = req.path;
  const method = req.method.toUpperCase();

  // 记录请求统计（异步，不阻塞请求）
  const recordStats = async () => {
    try {
      if (method === 'GET') {
        await statsManager.recordGetRequest(path, timestamp);
      } else if (method === 'POST') {
        await statsManager.recordPostRequest(path, timestamp);
      }
    } catch (error) {
      logger.error('Failed to record API stats', {
        module: MODULE_NAME,
        method: 'recordStats',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error',
        details: {
          path,
          method,
          timestamp
        }
      });
    }
  };

  // 异步记录统计，不等待结果
  recordStats();

  // 记录响应时间（可选）
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    
    // 如果响应时间过长，记录警告
    if (duration > 5000) { // 5秒
      logger.warn('Slow API response', {
        module: MODULE_NAME,
        method: 'responseTime',
        timestamp: getTimestamp(),
        details: {
          path,
          method,
          duration,
          statusCode: res.statusCode
        }
      });
    }
  });

  next();
};

/**
 * 批量统计中间件
 * 用于批量记录统计数据，减少 Redis 访问频率
 */
class BatchStatsMiddleware {
  private getBatch: Array<{ path: string; timestamp: number }> = [];
  private postBatch: Array<{ path: string; timestamp: number }> = [];
  private batchSize: number;
  private flushInterval: number;
  private flushTimer: NodeJS.Timeout | null = null;

  constructor(batchSize: number = 10, flushInterval: number = 5000) {
    this.batchSize = batchSize;
    this.flushInterval = flushInterval;
    this.startFlushTimer();
  }

  private startFlushTimer() {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }

    this.flushTimer = setInterval(() => {
      this.flush();
    }, this.flushInterval);
  }

  private async flush() {
    if (this.getBatch.length === 0 && this.postBatch.length === 0) {
      return;
    }

    try {
      const promises: Promise<void>[] = [];

      if (this.getBatch.length > 0) {
        promises.push(statsManager.recordGetRequests([...this.getBatch]));
        this.getBatch.length = 0;
      }

      if (this.postBatch.length > 0) {
        promises.push(statsManager.recordPostRequests([...this.postBatch]));
        this.postBatch.length = 0;
      }

      await Promise.all(promises);

      logger.debug('Batch stats flushed', {
        module: MODULE_NAME,
        method: 'flush',
        timestamp: getTimestamp()
      });
    } catch (error) {
      logger.error('Failed to flush batch stats', {
        module: MODULE_NAME,
        method: 'flush',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  public middleware = (req: Request, res: Response, next: NextFunction) => {
    const timestamp = Math.floor(Date.now() / 1000);
    const path = req.path;
    const method = req.method.toUpperCase();

    if (method === 'GET') {
      this.getBatch.push({ path, timestamp });
      if (this.getBatch.length >= this.batchSize) {
        this.flush();
      }
    } else if (method === 'POST') {
      this.postBatch.push({ path, timestamp });
      if (this.postBatch.length >= this.batchSize) {
        this.flush();
      }
    }

    next();
  };

  public destroy() {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = null;
    }
    this.flush(); // 最后一次刷新
  }
}

// 创建批量统计中间件实例
export const batchStatsMiddleware = new BatchStatsMiddleware();

/**
 * 统计路由中间件
 * 提供统计数据的 API 端点
 */
export const createStatsRoutes = () => {
  const router = require('express').Router();

  // 获取统计摘要
  router.get('/summary', async (req: Request, res: Response) => {
    try {
      const summary = await statsManager.getStatsSummary();
      res.json({
        success: true,
        data: {
          ...summary,
          uniquePaths: Array.from(summary.uniquePaths)
        }
      });
    } catch (error) {
      logger.error('Failed to get stats summary', {
        module: MODULE_NAME,
        method: 'getStatsSummary',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      res.status(500).json({
        success: false,
        error: 'Failed to get stats summary'
      });
    }
  });

  // 获取热门路径
  router.get('/top-paths', async (req: Request, res: Response) => {
    try {
      const limit = parseInt(req.query.limit as string) || 10;
      const topPaths = await statsManager.getTopPaths(limit);
      res.json({
        success: true,
        data: topPaths
      });
    } catch (error) {
      logger.error('Failed to get top paths', {
        module: MODULE_NAME,
        method: 'getTopPaths',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      res.status(500).json({
        success: false,
        error: 'Failed to get top paths'
      });
    }
  });

  // 获取当前小时统计
  router.get('/current-hour', async (req: Request, res: Response) => {
    try {
      const currentStats = await statsManager.getCurrentHourStats();
      res.json({
        success: true,
        data: {
          get: Object.fromEntries(currentStats.get),
          post: Object.fromEntries(currentStats.post)
        }
      });
    } catch (error) {
      logger.error('Failed to get current hour stats', {
        module: MODULE_NAME,
        method: 'getCurrentHourStats',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      res.status(500).json({
        success: false,
        error: 'Failed to get current hour stats'
      });
    }
  });

  // 获取每日统计
  router.get('/daily/:date', async (req: Request, res: Response) => {
    try {
      const date = new Date(req.params.date);
      if (isNaN(date.getTime())) {
        return res.status(400).json({
          success: false,
          error: 'Invalid date format'
        });
      }

      const dailyStats = await statsManager.getDailyStats(date);
      res.json({
        success: true,
        data: {
          date: date.toISOString().split('T')[0],
          get: Object.fromEntries(dailyStats.get),
          post: Object.fromEntries(dailyStats.post)
        }
      });
    } catch (error) {
      logger.error('Failed to get daily stats', {
        module: MODULE_NAME,
        method: 'getDailyStats',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      res.status(500).json({
        success: false,
        error: 'Failed to get daily stats'
      });
    }
  });

  // 导出统计数据
  router.get('/export', async (req: Request, res: Response) => {
    try {
      const startDate = new Date(req.query.start as string);
      const endDate = new Date(req.query.end as string);

      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        return res.status(400).json({
          success: false,
          error: 'Invalid date format'
        });
      }

      const exportData = await statsManager.exportStats(startDate, endDate);
      
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', `attachment; filename="stats-export-${Date.now()}.json"`);
      res.json(exportData);
    } catch (error) {
      logger.error('Failed to export stats', {
        module: MODULE_NAME,
        method: 'exportStats',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      res.status(500).json({
        success: false,
        error: 'Failed to export stats'
      });
    }
  });

  return router;
};

// 清理函数，在应用关闭时调用
export const cleanupStatsMiddleware = () => {
  batchStatsMiddleware.destroy();
};

// 进程退出时清理
process.on('SIGTERM', cleanupStatsMiddleware);
process.on('SIGINT', cleanupStatsMiddleware);
