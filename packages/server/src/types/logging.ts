import { LogLevel } from '@/configs/logging.js';

/**
 * 日志条目接口
 */
export interface LogEntry {
  id: string;
  level: LogLevel;
  message: string;
  timestamp: number;
  module?: string;
  method?: string;
  userId?: string;
  sessionId?: string;
  requestId?: string;
  metadata?: Record<string, any>;
  error?: {
    name: string;
    message: string;
    stack?: string;
    code?: string | number;
  };
  performance?: {
    duration?: number;
    memoryUsage?: NodeJS.MemoryUsage;
    cpuUsage?: NodeJS.CpuUsage;
  };
  context?: {
    ip?: string;
    userAgent?: string;
    path?: string;
    method?: string;
    statusCode?: number;
  };
}

/**
 * 批量日志条目
 */
export interface BatchLogEntry {
  entries: LogEntry[];
  batchId: string;
  timestamp: number;
  totalSize: number;
}

/**
 * 日志查询参数
 */
export interface LogQueryParams {
  // 时间范围
  startTime?: number;
  endTime?: number;
  
  // 日志级别
  levels?: LogLevel[];
  
  // 模块和方法
  modules?: string[];
  methods?: string[];
  
  // 用户和会话
  userIds?: string[];
  sessionIds?: string[];
  
  // 关键词搜索
  keyword?: string;
  
  // 分页
  page?: number;
  pageSize?: number;
  
  // 排序
  sortBy?: 'timestamp' | 'level';
  sortOrder?: 'asc' | 'desc';
  
  // 包含字段
  includeMetadata?: boolean;
  includeError?: boolean;
  includePerformance?: boolean;
  includeContext?: boolean;
}

/**
 * 日志查询结果
 */
export interface LogQueryResult {
  entries: LogEntry[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

/**
 * 日志统计参数
 */
export interface LogStatsParams {
  startTime?: number;
  endTime?: number;
  groupBy?: 'hour' | 'day' | 'level' | 'module';
  levels?: LogLevel[];
  modules?: string[];
}

/**
 * 日志统计结果
 */
export interface LogStatsResult {
  total: number;
  byLevel: Record<LogLevel, number>;
  byModule: Record<string, number>;
  byHour: Record<string, number>;
  byDay: Record<string, number>;
  errorRate: number;
  averageResponseTime?: number;
  topErrors: Array<{
    message: string;
    count: number;
    lastOccurrence: number;
  }>;
  performanceMetrics?: {
    averageMemoryUsage: number;
    peakMemoryUsage: number;
    averageCpuUsage: number;
  };
}

/**
 * 日志清理结果
 */
export interface LogCleanupResult {
  deletedEntries: number;
  deletedBytes: number;
  processedLevels: LogLevel[];
  duration: number;
  errors: string[];
}

/**
 * 日志压缩结果
 */
export interface LogCompressionResult {
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
  entriesCompressed: number;
  duration: number;
}

/**
 * 日志导出参数
 */
export interface LogExportParams {
  startTime: number;
  endTime: number;
  levels?: LogLevel[];
  modules?: string[];
  format?: 'json' | 'csv' | 'txt';
  includeMetadata?: boolean;
  maxEntries?: number;
}

/**
 * 日志导出结果
 */
export interface LogExportResult {
  data: string | LogEntry[];
  format: string;
  totalEntries: number;
  exportedEntries: number;
  fileSize: number;
  timestamp: number;
}

/**
 * 日志索引条目
 */
export interface LogIndexEntry {
  timestamp: number;
  level: LogLevel;
  module?: string;
  method?: string;
  userId?: string;
  hasError: boolean;
  entryKey: string;
}

/**
 * 日志批量操作结果
 */
export interface LogBatchOperationResult {
  success: boolean;
  processedCount: number;
  errorCount: number;
  errors: Array<{
    entryId: string;
    error: string;
  }>;
  duration: number;
}

/**
 * 日志健康检查结果
 */
export interface LogHealthCheckResult {
  status: 'healthy' | 'warning' | 'error';
  totalEntries: number;
  entriesByLevel: Record<LogLevel, number>;
  oldestEntry: number;
  newestEntry: number;
  storageUsage: {
    totalBytes: number;
    byLevel: Record<LogLevel, number>;
  };
  performance: {
    averageWriteTime: number;
    averageReadTime: number;
    errorRate: number;
  };
  issues: string[];
  recommendations: string[];
}

/**
 * 实时日志订阅参数
 */
export interface LogSubscriptionParams {
  levels?: LogLevel[];
  modules?: string[];
  keywords?: string[];
  userIds?: string[];
}

/**
 * 实时日志事件
 */
export interface LogEvent {
  type: 'new_entry' | 'batch_complete' | 'cleanup_complete' | 'error';
  data: LogEntry | BatchLogEntry | LogCleanupResult | Error;
  timestamp: number;
}
