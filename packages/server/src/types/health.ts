/**
 * 健康检查和服务状态相关的类型定义和枚举
 * 遵循 TypeScript 最佳实践和函数式编程原则
 */

/**
 * 服务健康状态枚举
 * 用于统一表示各种服务的健康状态
 */
export enum ServiceHealthStatus {
  /** 服务健康运行 */
  HEALTHY = 'healthy',
  /** 服务有警告但仍可用 */
  WARNING = 'warning',
  /** 服务不健康或不可用 */
  UNHEALTHY = 'unhealthy',
  /** 服务状态未知 */
  UNKNOWN = 'unknown',
  /** 服务错误 */
  ERROR = 'error'
}

/**
 * Redis 连接状态枚举
 */
export enum RedisConnectionStatus {
  /** 已断开连接 */
  DISCONNECTED = 'disconnected',
  /** 正在连接 */
  CONNECTING = 'connecting',
  /** 已连接 */
  CONNECTED = 'connected',
  /** 正在重连 */
  RECONNECTING = 'reconnecting',
  /** 连接错误 */
  ERROR = 'error'
}

/**
 * 降级策略枚举
 */
export enum FallbackStrategy {
  /** 写穿透：同时写入 Redis 和内存 */
  WRITE_THROUGH = 'write_through',
  /** 写回：先写入内存，定期同步到 Redis */
  WRITE_BACK = 'write_back',
  /** 仅内存：只使用内存存储 */
  MEMORY_ONLY = 'memory_only'
}

/**
 * 缓存策略枚举
 */
export enum CacheStrategy {
  /** 最近最少使用 */
  LRU = 'lru',
  /** 先进先出 */
  FIFO = 'fifo',
  /** 最近最少频率使用 */
  LFU = 'lfu'
}

/**
 * 服务类型枚举
 */
export enum ServiceType {
  /** Redis 连接服务 */
  REDIS_CONNECTION = 'redis_connection',
  /** 房间管理服务 */
  ROOM_SERVICE = 'room_service',
  /** 用户管理服务 */
  USER_SERVICE = 'user_service',
  /** 统计服务 */
  STATS_SERVICE = 'stats_service',
  /** 日志服务 */
  LOG_SERVICE = 'log_service',
  /** 缓存服务 */
  CACHE_SERVICE = 'cache_service',
  /** 随机用户服务 */
  RANDOM_USER_SERVICE = 'random_user_service'
}

/**
 * 健康检查常量
 */
export const HEALTH_CHECK_CONSTANTS = {
  /** 默认健康检查间隔（毫秒） */
  DEFAULT_CHECK_INTERVAL: 30000,
  /** 默认超时时间（毫秒） */
  DEFAULT_TIMEOUT: 5000,
  /** 默认重试次数 */
  DEFAULT_RETRY_COUNT: 3,
  /** 默认重试间隔（毫秒） */
  DEFAULT_RETRY_INTERVAL: 1000,
  /** 健康状态缓存时间（毫秒） */
  HEALTH_CACHE_TTL: 10000
} as const;

/**
 * 性能阈值常量
 */
export const PERFORMANCE_THRESHOLDS = {
  /** 缓存命中率警告阈值（百分比） */
  CACHE_HIT_RATE_WARNING: 60,
  /** 缓存命中率错误阈值（百分比） */
  CACHE_HIT_RATE_ERROR: 30,
  /** 错误率警告阈值（百分比） */
  ERROR_RATE_WARNING: 2,
  /** 错误率错误阈值（百分比） */
  ERROR_RATE_ERROR: 10,
  /** 响应时间警告阈值（毫秒） */
  RESPONSE_TIME_WARNING: 1000,
  /** 响应时间错误阈值（毫秒） */
  RESPONSE_TIME_ERROR: 5000,
  /** 内存使用率警告阈值（百分比） */
  MEMORY_USAGE_WARNING: 80,
  /** 内存使用率错误阈值（百分比） */
  MEMORY_USAGE_ERROR: 95
} as const;

/**
 * 基础健康检查结果接口
 */
export interface BaseHealthCheckResult {
  /** 健康状态 */
  status: ServiceHealthStatus;
  /** 检查时间戳 */
  timestamp: number;
  /** 问题列表 */
  issues: string[];
  /** 建议列表 */
  recommendations: string[];
}

/**
 * 服务健康检查结果接口
 */
export interface ServiceHealthCheckResult extends BaseHealthCheckResult {
  /** 服务类型 */
  serviceType: ServiceType;
  /** 服务名称 */
  serviceName: string;
  /** 响应时间（毫秒） */
  responseTime?: number;
  /** 错误信息 */
  error?: string;
}

/**
 * 连接指标接口
 */
export interface ConnectionMetrics {
  /** 总连接次数 */
  totalConnections: number;
  /** 总断开次数 */
  totalDisconnections: number;
  /** 总错误次数 */
  totalErrors: number;
  /** 最后连接时间 */
  lastConnectedAt: number;
  /** 最后断开时间 */
  lastDisconnectedAt: number;
  /** 运行时间（毫秒） */
  uptime: number;
  /** 当前状态 */
  status: RedisConnectionStatus;
  /** 最后错误信息 */
  lastError?: string;
  /** 重连尝试次数 */
  reconnectAttempts: number;
}

/**
 * 降级指标接口
 */
export interface FallbackMetrics {
  /** 内存命中次数 */
  memoryHits: number;
  /** 内存未命中次数 */
  memoryMisses: number;
  /** Redis 失败次数 */
  redisFailures: number;
  /** 降级激活次数 */
  fallbackActivations: number;
  /** 最后降级时间 */
  lastFallbackAt: number;
  /** 当前策略 */
  currentStrategy: FallbackStrategy;
  /** Redis 是否可用 */
  isRedisAvailable: boolean;
  /** 内存存储大小 */
  memoryStoreSize: number;
  /** 最大内存大小 */
  maxMemorySize: number;
}

/**
 * 缓存指标接口
 */
export interface CacheMetrics {
  /** 本地缓存命中次数 */
  localHits: number;
  /** 本地缓存未命中次数 */
  localMisses: number;
  /** Redis 缓存命中次数 */
  redisHits: number;
  /** Redis 缓存未命中次数 */
  redisMisses: number;
  /** 缓存淘汰次数 */
  evictions: number;
  /** 错误次数 */
  errors: number;
  /** 总请求次数 */
  totalRequests: number;
  /** 本地缓存大小 */
  localCacheSize: number;
  /** 命中率（百分比） */
  hitRate: number;
}

/**
 * 性能分析结果接口
 */
export interface PerformanceAnalysisResult {
  /** 总体摘要 */
  summary: {
    /** 总请求数 */
    totalRequests: number;
    /** 总命中数 */
    totalHits: number;
    /** 总错误数 */
    totalErrors: number;
    /** 总体命中率 */
    overallHitRate: number;
    /** 总体错误率 */
    overallErrorRate: number;
    /** 活跃缓存数量 */
    activeCaches: number;
  };
  /** 建议列表 */
  recommendations: string[];
  /** 问题列表 */
  issues: string[];
}

/**
 * 健康检查工具函数
 */
export const HealthCheckUtils = {
  /**
   * 根据指标确定健康状态
   */
  determineHealthStatus: (
    errorRate: number,
    responseTime: number,
    isConnected: boolean
  ): ServiceHealthStatus => {
    if (!isConnected) {
      return ServiceHealthStatus.UNHEALTHY;
    }
    
    if (errorRate >= PERFORMANCE_THRESHOLDS.ERROR_RATE_ERROR || 
        responseTime >= PERFORMANCE_THRESHOLDS.RESPONSE_TIME_ERROR) {
      return ServiceHealthStatus.ERROR;
    }
    
    if (errorRate >= PERFORMANCE_THRESHOLDS.ERROR_RATE_WARNING || 
        responseTime >= PERFORMANCE_THRESHOLDS.RESPONSE_TIME_WARNING) {
      return ServiceHealthStatus.WARNING;
    }
    
    return ServiceHealthStatus.HEALTHY;
  },

  /**
   * 创建基础健康检查结果
   */
  createBaseHealthResult: (
    status: ServiceHealthStatus,
    issues: string[] = [],
    recommendations: string[] = []
  ): BaseHealthCheckResult => ({
    status,
    timestamp: Date.now(),
    issues,
    recommendations
  }),

  /**
   * 格式化运行时间
   */
  formatUptime: (uptimeMs: number): string => {
    const seconds = Math.floor(uptimeMs / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days}d ${hours % 24}h ${minutes % 60}m`;
    if (hours > 0) return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  },

  /**
   * 计算命中率
   */
  calculateHitRate: (hits: number, total: number): number => {
    return total > 0 ? Math.round((hits / total) * 10000) / 100 : 0;
  },

  /**
   * 计算错误率
   */
  calculateErrorRate: (errors: number, total: number): number => {
    return total > 0 ? Math.round((errors / total) * 10000) / 100 : 0;
  }
} as const;
