/**
 * Redis 健康检查和服务统计相关的类型定义
 * 遵循 TypeScript 最佳实践和函数式编程原则
 */

import type { 
  ServiceHealthStatus, 
  RedisConnectionStatus, 
  ConnectionMetrics, 
  FallbackMetrics,
  ServiceType,
  BaseHealthCheckResult 
} from './health.js';

/**
 * Redis 服务健康检查结果接口
 */
export interface RedisServiceHealthResult {
  /** 是否健康 */
  isHealthy: boolean;
  /** 各服务状态 */
  services: Record<string, boolean>;
  /** 错误信息 */
  error?: string;
}

/**
 * Redis 服务统计信息接口
 */
export interface RedisServiceStats {
  /** 房间数量 */
  rooms: number;
  /** 用户数量 */
  users: number;
  /** 统计时间戳信息 */
  statsTimestamps: {
    /** GET 请求时间戳数量 */
    get: number;
    /** POST 请求时间戳数量 */
    post: number;
  };
  /** 内存使用情况 */
  memory?: string;
}

/**
 * Redis 连接详细指标接口
 * 扩展基础连接指标，添加 Redis 特定信息
 */
export interface RedisConnectionMetrics extends ConnectionMetrics {
  /** Redis 版本信息 */
  redisVersion?: string;
  /** 数据库大小 */
  dbSize?: number;
  /** 已使用内存 */
  usedMemory?: number;
  /** 最大内存 */
  maxMemory?: number;
  /** 连接的客户端数量 */
  connectedClients?: number;
  /** 阻塞的客户端数量 */
  blockedClients?: number;
}

/**
 * Redis 整体健康报告接口
 */
export interface RedisHealthReport {
  /** 整体状态 */
  overall: {
    /** 健康状态 */
    status: ServiceHealthStatus;
    /** Redis 是否可用 */
    isRedisAvailable: boolean;
    /** 连接状态 */
    connectionStatus: RedisConnectionStatus;
    /** 是否启用降级模式 */
    fallbackActive: boolean;
  };
  /** 连接信息 */
  connection: RedisConnectionMetrics;
  /** 服务健康状态 */
  services: RedisServiceHealthResult;
  /** 服务统计信息 */
  stats: RedisServiceStats;
  /** 降级指标 */
  fallback: FallbackMetrics;
  /** 报告生成时间戳 */
  timestamp: number;
}

/**
 * Redis 诊断信息接口
 */
export interface RedisDiagnosticInfo {
  /** 基础健康检查结果 */
  health: BaseHealthCheckResult;
  /** 连接指标 */
  connectionMetrics: RedisConnectionMetrics;
  /** 服务健康状态 */
  serviceHealth: RedisServiceHealthResult;
  /** 降级指标 */
  fallbackMetrics: FallbackMetrics;
  /** 性能指标 */
  performance: {
    /** 平均响应时间 */
    averageResponseTime: number;
    /** 最大响应时间 */
    maxResponseTime: number;
    /** 最小响应时间 */
    minResponseTime: number;
    /** 吞吐量（每秒操作数） */
    throughput: number;
  };
  /** 配置信息 */
  configuration: {
    /** 最大连接数 */
    maxConnections: number;
    /** 超时设置 */
    timeout: number;
    /** 重试配置 */
    retryConfig: {
      /** 最大重试次数 */
      maxRetries: number;
      /** 重试延迟 */
      retryDelay: number;
    };
  };
}

/**
 * Redis 服务状态枚举
 */
export enum RedisServiceStatus {
  /** 服务正常运行 */
  RUNNING = 'running',
  /** 服务已停止 */
  STOPPED = 'stopped',
  /** 服务启动中 */
  STARTING = 'starting',
  /** 服务停止中 */
  STOPPING = 'stopping',
  /** 服务错误 */
  ERROR = 'error'
}

/**
 * Redis 操作类型枚举
 */
export enum RedisOperationType {
  /** 读操作 */
  READ = 'read',
  /** 写操作 */
  WRITE = 'write',
  /** 删除操作 */
  DELETE = 'delete',
  /** 批量操作 */
  BATCH = 'batch',
  /** 事务操作 */
  TRANSACTION = 'transaction'
}

/**
 * Redis 操作结果接口
 */
export interface RedisOperationResult<T = any> {
  /** 操作是否成功 */
  success: boolean;
  /** 返回数据 */
  data?: T;
  /** 错误信息 */
  error?: string;
  /** 操作类型 */
  operationType: RedisOperationType;
  /** 操作耗时（毫秒） */
  duration: number;
  /** 操作时间戳 */
  timestamp: number;
}

/**
 * Redis 批量操作结果接口
 */
export interface RedisBatchOperationResult {
  /** 总操作数 */
  totalOperations: number;
  /** 成功操作数 */
  successfulOperations: number;
  /** 失败操作数 */
  failedOperations: number;
  /** 失败详情 */
  failures: Array<{
    /** 操作索引 */
    index: number;
    /** 错误信息 */
    error: string;
  }>;
  /** 总耗时（毫秒） */
  totalDuration: number;
  /** 平均耗时（毫秒） */
  averageDuration: number;
}

/**
 * Redis 监控指标接口
 */
export interface RedisMonitoringMetrics {
  /** 命令统计 */
  commandStats: Record<string, {
    /** 调用次数 */
    calls: number;
    /** 总耗时 */
    totalTime: number;
    /** 平均耗时 */
    averageTime: number;
  }>;
  /** 内存统计 */
  memoryStats: {
    /** 已使用内存（字节） */
    usedMemory: number;
    /** 内存峰值（字节） */
    peakMemory: number;
    /** 内存碎片率 */
    fragmentationRatio: number;
  };
  /** 客户端统计 */
  clientStats: {
    /** 连接的客户端数 */
    connectedClients: number;
    /** 阻塞的客户端数 */
    blockedClients: number;
    /** 最大客户端数 */
    maxClients: number;
  };
  /** 键空间统计 */
  keyspaceStats: Record<string, {
    /** 键数量 */
    keys: number;
    /** 过期键数量 */
    expires: number;
    /** 平均 TTL */
    avgTtl: number;
  }>;
}

/**
 * Redis 健康检查配置接口
 */
export interface RedisHealthCheckConfig {
  /** 检查间隔（毫秒） */
  checkInterval: number;
  /** 超时时间（毫秒） */
  timeout: number;
  /** 重试次数 */
  retryCount: number;
  /** 重试间隔（毫秒） */
  retryInterval: number;
  /** 是否启用详细监控 */
  enableDetailedMonitoring: boolean;
  /** 是否启用性能监控 */
  enablePerformanceMonitoring: boolean;
}

/**
 * Redis 健康检查常量
 */
export const REDIS_HEALTH_CONSTANTS = {
  /** 默认 ping 超时时间 */
  DEFAULT_PING_TIMEOUT: 5000,
  /** 默认健康检查间隔 */
  DEFAULT_HEALTH_CHECK_INTERVAL: 30000,
  /** 默认最大重连尝试次数 */
  DEFAULT_MAX_RECONNECT_ATTEMPTS: 10,
  /** 默认重连延迟 */
  DEFAULT_RECONNECT_DELAY: 1000,
  /** 健康状态缓存 TTL */
  HEALTH_STATUS_CACHE_TTL: 10000,
  /** 性能指标收集间隔 */
  METRICS_COLLECTION_INTERVAL: 60000
} as const;

/**
 * Redis 工具函数
 */
export const RedisHealthUtils = {
  /**
   * 判断 Redis 连接是否健康
   */
  isConnectionHealthy: (status: RedisConnectionStatus): boolean => {
    return status === RedisConnectionStatus.CONNECTED;
  },

  /**
   * 格式化内存大小
   */
  formatMemorySize: (bytes: number): string => {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${Math.round(size * 100) / 100} ${units[unitIndex]}`;
  },

  /**
   * 计算内存碎片率
   */
  calculateFragmentationRatio: (usedMemory: number, rssMemory: number): number => {
    return rssMemory > 0 ? Math.round((rssMemory / usedMemory) * 100) / 100 : 0;
  },

  /**
   * 创建默认健康检查配置
   */
  createDefaultHealthConfig: (): RedisHealthCheckConfig => ({
    checkInterval: REDIS_HEALTH_CONSTANTS.DEFAULT_HEALTH_CHECK_INTERVAL,
    timeout: REDIS_HEALTH_CONSTANTS.DEFAULT_PING_TIMEOUT,
    retryCount: 3,
    retryInterval: 1000,
    enableDetailedMonitoring: true,
    enablePerformanceMonitoring: true
  }),

  /**
   * 评估 Redis 性能状态
   */
  evaluatePerformanceStatus: (metrics: RedisMonitoringMetrics): ServiceHealthStatus => {
    const { memoryStats, clientStats } = metrics;
    
    // 检查内存碎片率
    if (memoryStats.fragmentationRatio > 2.0) {
      return ServiceHealthStatus.WARNING;
    }
    
    // 检查客户端连接数
    const clientUsageRatio = clientStats.connectedClients / clientStats.maxClients;
    if (clientUsageRatio > 0.9) {
      return ServiceHealthStatus.WARNING;
    }
    
    if (clientUsageRatio > 0.95) {
      return ServiceHealthStatus.ERROR;
    }
    
    return ServiceHealthStatus.HEALTHY;
  }
} as const;
