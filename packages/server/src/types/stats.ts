/**
 * 统计服务相关的类型定义
 * 遵循 TypeScript 最佳实践和函数式编程原则
 */

/**
 * 统计数据类型枚举
 */
export enum StatsDataType {
  /** GET 请求统计 */
  GET_REQUESTS = 'get_requests',
  /** POST 请求统计 */
  POST_REQUESTS = 'post_requests',
  /** 用户活动统计 */
  USER_ACTIVITY = 'user_activity',
  /** 房间活动统计 */
  ROOM_ACTIVITY = 'room_activity',
  /** 错误统计 */
  ERROR_STATS = 'error_stats',
  /** 性能统计 */
  PERFORMANCE_STATS = 'performance_stats'
}

/**
 * 统计时间粒度枚举
 */
export enum StatsTimeGranularity {
  /** 分钟级 */
  MINUTE = 'minute',
  /** 小时级 */
  HOUR = 'hour',
  /** 天级 */
  DAY = 'day',
  /** 周级 */
  WEEK = 'week',
  /** 月级 */
  MONTH = 'month'
}

/**
 * 统计聚合类型枚举
 */
export enum StatsAggregationType {
  /** 求和 */
  SUM = 'sum',
  /** 平均值 */
  AVERAGE = 'average',
  /** 最大值 */
  MAX = 'max',
  /** 最小值 */
  MIN = 'min',
  /** 计数 */
  COUNT = 'count',
  /** 百分位数 */
  PERCENTILE = 'percentile'
}

/**
 * 请求统计记录接口
 */
export interface RequestStatsRecord {
  /** 请求路径 */
  path: string;
  /** 请求次数 */
  count: number;
  /** 时间戳 */
  timestamp: number;
}

/**
 * 统计摘要接口
 */
export interface StatsSummary {
  /** 总 GET 请求数 */
  totalGetRequests: number;
  /** 总 POST 请求数 */
  totalPostRequests: number;
  /** 唯一路径集合 */
  uniquePaths: Set<string>;
  /** 时间范围 */
  timeRange: {
    /** 开始时间 */
    start: number;
    /** 结束时间 */
    end: number;
  } | null;
  /** 最活跃的路径 */
  topPaths: Array<{
    /** 路径 */
    path: string;
    /** 请求次数 */
    count: number;
    /** 请求类型 */
    method: 'GET' | 'POST';
  }>;
  /** 统计生成时间 */
  generatedAt: number;
}

/**
 * 每日统计数据接口
 */
export interface DailyStats {
  /** GET 请求统计 */
  get: Map<string, number>;
  /** POST 请求统计 */
  post: Map<string, number>;
  /** 统计日期 */
  date: string;
  /** 总请求数 */
  totalRequests: number;
  /** 唯一路径数 */
  uniquePathsCount: number;
}

/**
 * 当前小时统计数据接口
 */
export interface CurrentHourStats {
  /** GET 请求统计 */
  get: Map<string, number>;
  /** POST 请求统计 */
  post: Map<string, number>;
  /** 统计小时（时间戳） */
  hour: number;
  /** 更新时间 */
  lastUpdated: number;
}

/**
 * 统计时间戳信息接口
 */
export interface StatsTimestamps {
  /** GET 请求时间戳列表 */
  get: number[];
  /** POST 请求时间戳列表 */
  post: number[];
}

/**
 * 统计范围查询参数接口
 */
export interface StatsRangeQuery {
  /** 开始时间戳 */
  startTimestamp: number;
  /** 结束时间戳 */
  endTimestamp: number;
  /** 路径过滤器 */
  pathFilter?: string[];
  /** 聚合类型 */
  aggregationType?: StatsAggregationType;
  /** 时间粒度 */
  granularity?: StatsTimeGranularity;
}

/**
 * 统计范围查询结果接口
 */
export interface StatsRangeResult {
  /** GET 请求统计 */
  getStats: Map<number, Map<string, number>>;
  /** POST 请求统计 */
  postStats: Map<number, Map<string, number>>;
  /** 查询参数 */
  query: StatsRangeQuery;
  /** 结果元数据 */
  metadata: {
    /** 总数据点数 */
    totalDataPoints: number;
    /** 查询耗时（毫秒） */
    queryDuration: number;
    /** 数据完整性 */
    dataCompleteness: number; // 0-1 之间的值
  };
}

/**
 * 路径统计详情接口
 */
export interface PathStatsDetail {
  /** 路径名称 */
  path: string;
  /** 统计数据 */
  stats: Array<{
    /** 时间戳 */
    timestamp: number;
    /** 请求次数 */
    count: number;
    /** 请求类型 */
    method: 'GET' | 'POST';
  }>;
  /** 总请求数 */
  totalRequests: number;
  /** 平均每小时请求数 */
  averageRequestsPerHour: number;
  /** 峰值请求数 */
  peakRequests: number;
  /** 峰值时间 */
  peakTime: number;
}

/**
 * 统计清理结果接口
 */
export interface StatsCleanupResult {
  /** 清理的条目数 */
  deletedEntries: number;
  /** 清理的时间戳数 */
  deletedTimestamps: number;
  /** 清理耗时（毫秒） */
  duration: number;
  /** 清理的数据类型 */
  cleanedDataTypes: StatsDataType[];
  /** 错误列表 */
  errors: string[];
}

/**
 * 统计性能指标接口
 */
export interface StatsPerformanceMetrics {
  /** 写入性能 */
  writePerformance: {
    /** 平均写入时间（毫秒） */
    averageWriteTime: number;
    /** 最大写入时间（毫秒） */
    maxWriteTime: number;
    /** 写入成功率 */
    writeSuccessRate: number;
    /** 每秒写入数 */
    writesPerSecond: number;
  };
  /** 读取性能 */
  readPerformance: {
    /** 平均读取时间（毫秒） */
    averageReadTime: number;
    /** 最大读取时间（毫秒） */
    maxReadTime: number;
    /** 读取成功率 */
    readSuccessRate: number;
    /** 每秒读取数 */
    readsPerSecond: number;
  };
  /** 存储效率 */
  storageEfficiency: {
    /** 数据压缩率 */
    compressionRatio: number;
    /** 存储利用率 */
    storageUtilization: number;
    /** 索引效率 */
    indexEfficiency: number;
  };
}

/**
 * 统计健康检查结果接口
 */
export interface StatsHealthCheckResult {
  /** 健康状态 */
  status: 'healthy' | 'warning' | 'error';
  /** 数据完整性检查 */
  dataIntegrity: {
    /** 是否完整 */
    isComplete: boolean;
    /** 缺失的时间段 */
    missingTimeRanges: Array<{
      start: number;
      end: number;
    }>;
    /** 数据一致性 */
    consistency: number; // 0-1 之间的值
  };
  /** 性能指标 */
  performance: StatsPerformanceMetrics;
  /** 存储使用情况 */
  storage: {
    /** 总存储大小（字节） */
    totalSize: number;
    /** 已使用存储（字节） */
    usedSize: number;
    /** 存储增长率（每天） */
    growthRate: number;
  };
  /** 问题列表 */
  issues: string[];
  /** 建议列表 */
  recommendations: string[];
  /** 检查时间 */
  checkedAt: number;
}

/**
 * 统计配置接口
 */
export interface StatsConfig {
  /** 数据保留天数 */
  retentionDays: number;
  /** 聚合间隔（秒） */
  aggregationInterval: number;
  /** 是否启用压缩 */
  enableCompression: boolean;
  /** 批量写入大小 */
  batchSize: number;
  /** 清理间隔（小时） */
  cleanupInterval: number;
  /** 性能监控配置 */
  performanceMonitoring: {
    /** 是否启用 */
    enabled: boolean;
    /** 采样率 */
    samplingRate: number;
    /** 指标收集间隔（秒） */
    metricsInterval: number;
  };
}

/**
 * 统计常量
 */
export const STATS_CONSTANTS = {
  /** 默认数据保留天数 */
  DEFAULT_RETENTION_DAYS: 30,
  /** 默认聚合间隔（秒） */
  DEFAULT_AGGREGATION_INTERVAL: 3600,
  /** 默认批量大小 */
  DEFAULT_BATCH_SIZE: 100,
  /** 默认清理间隔（小时） */
  DEFAULT_CLEANUP_INTERVAL: 24,
  /** 最大路径长度 */
  MAX_PATH_LENGTH: 255,
  /** 最大统计条目数 */
  MAX_STATS_ENTRIES: 10000,
  /** 性能阈值 */
  PERFORMANCE_THRESHOLDS: {
    /** 写入时间警告阈值（毫秒） */
    WRITE_TIME_WARNING: 100,
    /** 写入时间错误阈值（毫秒） */
    WRITE_TIME_ERROR: 500,
    /** 读取时间警告阈值（毫秒） */
    READ_TIME_WARNING: 50,
    /** 读取时间错误阈值（毫秒） */
    READ_TIME_ERROR: 200,
    /** 成功率警告阈值 */
    SUCCESS_RATE_WARNING: 0.95,
    /** 成功率错误阈值 */
    SUCCESS_RATE_ERROR: 0.90
  }
} as const;

/**
 * 统计工具函数
 */
export const StatsUtils = {
  /**
   * 格式化时间戳为可读字符串
   */
  formatTimestamp: (timestamp: number, granularity: StatsTimeGranularity): string => {
    const date = new Date(timestamp * 1000);
    
    switch (granularity) {
      case StatsTimeGranularity.MINUTE:
        return date.toISOString().slice(0, 16);
      case StatsTimeGranularity.HOUR:
        return date.toISOString().slice(0, 13);
      case StatsTimeGranularity.DAY:
        return date.toISOString().slice(0, 10);
      case StatsTimeGranularity.WEEK:
        const weekStart = new Date(date);
        weekStart.setDate(date.getDate() - date.getDay());
        return weekStart.toISOString().slice(0, 10);
      case StatsTimeGranularity.MONTH:
        return date.toISOString().slice(0, 7);
      default:
        return date.toISOString();
    }
  },

  /**
   * 计算时间范围内的数据点数
   */
  calculateDataPoints: (
    startTime: number, 
    endTime: number, 
    granularity: StatsTimeGranularity
  ): number => {
    const duration = endTime - startTime;
    
    switch (granularity) {
      case StatsTimeGranularity.MINUTE:
        return Math.ceil(duration / 60);
      case StatsTimeGranularity.HOUR:
        return Math.ceil(duration / 3600);
      case StatsTimeGranularity.DAY:
        return Math.ceil(duration / 86400);
      case StatsTimeGranularity.WEEK:
        return Math.ceil(duration / 604800);
      case StatsTimeGranularity.MONTH:
        return Math.ceil(duration / 2592000); // 30天近似
      default:
        return 1;
    }
  },

  /**
   * 验证路径格式
   */
  validatePath: (path: string): boolean => {
    return path.length > 0 && 
           path.length <= STATS_CONSTANTS.MAX_PATH_LENGTH &&
           /^\/[a-zA-Z0-9\-_\/]*$/.test(path);
  },

  /**
   * 创建默认统计配置
   */
  createDefaultConfig: (): StatsConfig => ({
    retentionDays: STATS_CONSTANTS.DEFAULT_RETENTION_DAYS,
    aggregationInterval: STATS_CONSTANTS.DEFAULT_AGGREGATION_INTERVAL,
    enableCompression: true,
    batchSize: STATS_CONSTANTS.DEFAULT_BATCH_SIZE,
    cleanupInterval: STATS_CONSTANTS.DEFAULT_CLEANUP_INTERVAL,
    performanceMonitoring: {
      enabled: true,
      samplingRate: 0.1,
      metricsInterval: 300
    }
  })
} as const;
