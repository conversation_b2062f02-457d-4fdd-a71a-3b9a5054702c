import { Server, type RemoteSocket } from 'socket.io';
import {
    type ServerSocket, type JoinRoomPayload, type LeaveRoomPayload, type SendMessagePayload,
    type RoomUpdatedPayload, type MessageDeliveredPayload, type Room, type User,
    type HistorySharePayload, type DeliveredMessageAckPayload,
    type UserId,
    type KickUserPayload,
    type UserKickedPayload,
    type UserJoinedPayload,
    type ClientEmitEvents,
    type UserLeftPayload,
    type ServerEmitEvents,
    type ServerSocketData,
    type ServerStatusPayload,
    type ClientPayload,
    EServerAckType,
    type AckPayload,
    type InvitePayload
} from '@unibabble/shared';
import {
    EMessageState, EMessageType, getTimestamp, logger, WebSocketEvents, createSerErr,
    genUserMsgId, ErrorCode, RoomActiveStatus, unknown__serErr,
    findSocketByUserId,
    UserLangUtils,
    EChatModeType
} from '@unibabble/shared';
import type { RoomWithStatus, UserRoomState, UserWithStatus } from "@/types/index.js";
import { serConfig } from '@/configs/index.js';
import { io, serverState } from '@/index.js';
import {
    genRoomId, emitErr, emitSys, emitToRoom, emitPayload,
    genUserToken
} from '@/utils/index.js';
import { roomManager as redisRoomManager } from '@/managers/roomManager.js';
import { userManager as redisUserManager } from '@/managers/userManager.js';
import { messageManager as redisMessageManager } from '@/managers/messageManager.js';


const MODULE_NAME = 'Server.Ws';

function getClientIP(socket: any) {
    const xForwardedFor = socket.handshake.headers['x-forwarded-for'];
    if (xForwardedFor) {
        return xForwardedFor.split(',')[0].trim();
    }
    const remoteAddress = socket.conn.remoteAddress;
    if (remoteAddress) {
        return remoteAddress.replace(/^::ffff:/, '');
    }
    return socket.handshake.address;
}

const initializeWebSocketIO = (httpServer: any) => {

    const io = new Server(httpServer, {
        // config ws path, as same as client configured ws path
        path: serConfig.server.ws,
        cors: {
            origin: serConfig.cors.origin,
            methods: serConfig.cors.methods,
            credentials: true
        },
        pingInterval: serConfig.connection.pingInterval,
        pingTimeout: serConfig.connection.pingTimeout
    });

    // registed events handler
    io.on(WebSocketEvents.STANDARD.SERVER.CONNECTION, (socket: ServerSocket) => {
        const clientIP = getClientIP(socket);
        const timestamp = getTimestamp();

        logger.info(WebSocketEvents.STANDARD.SERVER.CONNECTION, {
            module: MODULE_NAME,
            method: 'serverIO.on:WebSocketEvents.STANDARD.SERVER.CONNECTION',
            timestamp,
            details: {
                socketId: socket.id,
                ip: clientIP
            }
        });

        // 用户connect后, 服务端应保存用户状态


        socket.on(WebSocketEvents.ROOM.JOIN, (data) => handleRoomJoin(io, socket, data));
        socket.on(WebSocketEvents.ROOM.LEAVE, (data) => handleRoomLeave(io, socket, data));
        socket.on(WebSocketEvents.MESSAGE.SEND.SENDING, (data) => messageManager.handleMessageSend(io, socket, data));
        socket.on(WebSocketEvents.MESSAGE.SEND.ACK, messageManager.handleMessageAck);
        socket.on(WebSocketEvents.MESSAGE.HISTORY.SHARE, (data) => messageManager.handleHistoryShare(io, socket, data));
        socket.on(WebSocketEvents.SERVER.STATUS, async (data, callback) => await handleServerStatus(socket, data, callback));
        socket.on(WebSocketEvents.SETTINGS, async (data, callback) => await handleSettings(socket, data, callback));
        socket.on(WebSocketEvents.STANDARD.CLIENT.DISCONNECT, (reason) => handleDisconnect(socket, reason));
        socket.on(WebSocketEvents.USER.KICK, (data) => handleKickUser(io, socket, data));
        socket.on(WebSocketEvents.ROOM.RANDOM.MATCH, (data) => startRandom(socket, data));
    });

    return io
}


// 使用 Redis-based roomManager
const roomManager = redisRoomManager;


// 扩展 Redis-based messageManager 以包含现有的方法
const messageManager = {
    ...redisMessageManager,

    async handleMessageSend(io: Server, ss: ServerSocket, data: SendMessagePayload): Promise<void> {
        const method = 'handleMessageSend';
        const { messageId, content, roomId, userId, lang, fstSendTimestamp } = data;
        const sendMsgSerErr = (
            code: ErrorCode,
            msg: string,
            details?: Record<string, unknown>,
            eventType?: keyof ClientEmitEvents
        ) => createSerErr(
            code,
            msg,
            method,
            MODULE_NAME,
            roomId,
            userId,
            eventType,
            details
        )

        try {
            const user = await userManager.getUser(userId);
            const room = await roomManager.getRoom(roomId);
            const timestamp = getTimestamp()

            if (!user) {
                throw sendMsgSerErr(
                    ErrorCode.USER_NOT_EXISTS,
                    `'UserId: ${userId} is not in the Room: ${roomId}'`
                )
            }

            if (!room) {
                throw sendMsgSerErr(
                    ErrorCode.ROOM_NOT_FOUND,
                    `'Room Not Found, RoomId: ${roomId}'`,
                    {
                        messageId,
                        content,
                        roomId,
                        userId,
                        lang
                    }
                )
            }

            if (!(await this.checkMessageRateLimit(ss))) {
                throw sendMsgSerErr(
                    ErrorCode.MESSAGE_SEND_RATE_LIMIT_EXCEEDED,
                    'Message sending rate is too high',
                    {
                        messageId,
                        content,
                        roomId,
                        userId,
                        lang,
                        fstCheckAt: timestamp
                    }
                )
            }

            logger.info('Client send a message to server.', {
                module: MODULE_NAME,
                method,
                timestamp,
                roomId,
                userId,
                details: {
                    messageId,
                    lang,
                    content
                }
            });

            if (lang) {
                await userManager.updateUserLanguage(userId, lang);
                user.lang = lang;
            }

            const payload: MessageDeliveredPayload = {
                timestamp,
                roomId: room.id,
                user: {
                    id: user.id,
                    name: user.name,
                    lang: user.lang,
                },
                messageWithState: {
                    id: messageId ?? genUserMsgId(roomId, user.id),
                    type: EMessageType.TEXT,
                    content,
                    sender: user as User,
                    timestamp: fstSendTimestamp ?? timestamp,
                    roomId,
                    state: {
                        state: EMessageState.DELIVERED,
                        stateAt: timestamp
                    }
                }
            }
            emitToRoom(
                io,
                room.id,
                WebSocketEvents.MESSAGE.SEND.DELIVERED,
                payload,
            )

        } catch (err) {
            const serverErr = unknown__serErr(err, ErrorCode.MESSAGE_SEND_FAILED);
            emitErr(
                ss,
                serverErr.code,
                WebSocketEvents.ERROR.MESSAGE.SEND_FAILED,
                serverErr.message,
                method,
                MODULE_NAME,
                data.roomId,
                data.userId,
                serverErr.details
            );
        }
    },

    async handleHistoryShare(io: Server, socket: ServerSocket, data: HistorySharePayload) {
        const method = 'handleHistoryShare';
        const { roomId, fromUserId, targetUserId, histories } = data;

        // 转发历史消息给目标用户
        const errorCallBack = () => {
            emitErr(
                socket,
                ErrorCode.USER_NOT_EXISTS,
                WebSocketEvents.ERROR.USER.NOT_EXISTS,
                `'User: ${targetUserId} not exists'`,
                method,
                MODULE_NAME,
                roomId,
                targetUserId
            );
        }

        const payload: HistorySharePayload = {
            roomId,
            fromUserId,
            histories,
            targetUserId,
            timestamp: getTimestamp()
        }

        const targetSocket = await findSocketByUserId(io, targetUserId, roomId)

        targetSocket?.emit(WebSocketEvents.MESSAGE.HISTORY.OFFER, payload)
    }
};


// 使用 Redis-based userManager
const userManager = redisUserManager;

async function handleDisconnect(socket: ServerSocket, reason: string, room?: Room) {
    const method = 'handleDisconnect';
    /*
    除非主动disconnect, 系统内产生的disconnect不应对用户进行处理, client在尝试重连
    一旦断连, 如果这个用户无法再次连接, 应从room清理这个用户, 当该用户再次join这room时, 有足够的空间来join
    */
    switch (reason) {
        case ErrorCode.SERVER_PING_TIMEOUT:
            console.log('  Client timed out.');
            // 可以记录超时日志，或者尝试重新连接（通常由客户端自动处理）
            break;
        case ErrorCode.SERVER_TRANSPORT_CLOSE:
            console.log('  Server transport connection closed.');
            // 可以记录连接关闭日志
            break;
        case ErrorCode.SERVER_TRANSPORT_ERROR:
            console.log('  Server transport error occurred.');
            // 可以记录错误日志，并尝试分析错误原因
            break;
        case ErrorCode.SERVER_IO_SERVER_DISCONNECT:
            console.log('  Server disconnected the client.');
            // 可以记录服务器主动断开连接的原因
            break;
        case ErrorCode.SERVER_IO_CLIENT_DISCONNECT:
            console.log('  Client disconnected itself.');
            // 可以记录客户端主动断开连接的日志
            // 从随机池中移除用户
            if (socket.data.userId) {
                serverState.randomUser.delete(socket.data.userId);
                await userManager.deleteUser(socket.data.userId);
                logger.info(`用户断线，从随机池中移除: ${socket.data.userId}`, {
                    module: MODULE_NAME,
                    method,
                    timestamp: getTimestamp(),
                    details: { reason }
                });
            }
            break;
        case ErrorCode.SERVER_PARSE_ERROR:
            console.log('  parse error occurred.');
            break;
        case ErrorCode.SERVER_FORCE_DISCONNECT:
            if (socket.data.userId) {
                const user = await userManager.getUser(socket.data.userId);
                if (user) {
                    if (room) {
                        emitSys(
                            socket,
                            WebSocketEvents.ACTION.SERVER.FORCEDISCONNECT,
                            `User:${user.id} is forcibly disconnected because the room:${room.id} in which the user resides has been idle for more than ${serConfig.room.roomMaxIdle / 1000 / 60} minutes`,
                            room.id,
                            { userId: user.id }
                        )
                    }
                }
            } else {
                logger.error(`socket no bound data`, {
                    timestamp: getTimestamp(),
                    module: MODULE_NAME,
                    method,
                    details: {
                        reason
                    }
                })
            }

            break;
        default:
            // 对于其他原因的断线，也从随机池中移除用户
            if (socket.data.userId) {
                serverState.randomUser.delete(socket.data.userId);
                logger.info(`用户断线 (原因: ${reason})，从随机池中移除: ${socket.data.userId}`, {
                    module: MODULE_NAME,
                    method,
                    timestamp: getTimestamp(),
                    details: { reason }
                });
            }
            break;
    }
}

async function handleKickUser(io: Server, socket: ServerSocket, data: KickUserPayload) {
    const method = 'handleKickUser';
    const { roomId, kickee, user: kicker, userToken } = data;
    const kickSerErr = (code: ErrorCode, msg: string) => createSerErr(
        code,
        msg,
        method,
        MODULE_NAME,
        roomId,
        kicker.id
    )

    try {
        let room = await roomManager.getRoom(roomId);
        if (!room) {
            throw kickSerErr(
                ErrorCode.ROOM_NOT_FOUND,
                `'Room: ${roomId} not found`
            )
        }

        /*
        主要是防止非管理员冒用adminId来处理, 验证是否是admin的userToken来判断权限
        */
        if (!(await userManager.checkAdminToken(kicker.id, roomId, userToken))) {
            throw kickSerErr(
                ErrorCode.ROOM_ADMIN_ONLY,
                `User:${kicker.id} is not the admin of room:${roomId}`
            )
        }

        // 踢出用户
        for (const kickeeId of kickee) {
            const kickeeSocket = await findSocketByUserId(io, kickeeId, roomId);
            if (kickeeSocket) {
                const kdpl: UserKickedPayload = {
                    isKicked: true,
                    kickee: kickeeId,
                    user: kicker,
                    timestamp: getTimestamp(),
                    roomId
                }
                emitPayload(kickeeSocket, WebSocketEvents.USER.KICKED, kdpl);
                room = await roomManager.leaveRoom(kickeeSocket, room, kickeeId);
            }

            // 添加到被踢用户列表
            await roomManager.kickUser(roomId, kickeeId);
        }

        // 获取更新后的房间信息
        room = await roomManager.getRoom(roomId);
        if (room) {
            const rupl: RoomUpdatedPayload = {
                timestamp: getTimestamp(),
                roomId,
                users: room.users,
                lastActiveAt: room.lastActiveAt,
                status: room.status,
                kickedUserIds: kickee
            }
            emitToRoom(io, roomId, WebSocketEvents.ROOM.UPDATE, rupl);
        }

    } catch (e) {
        const kickedNames = kickee.map(k => serverState.users.get(k)?.name).filter(Boolean).join(', ')
        const error = unknown__serErr(
            e,
            ErrorCode.USER_KICK_ERROR,
            method,
            MODULE_NAME,
            `unknown error when ${socket.data.userName ?? kicker.id} kicking user: ${kickedNames}`
        )

        emitErr(
            socket,
            ErrorCode.ROOM_LEAVE_FAILED,
            WebSocketEvents.ERROR.ROOM.LEAVE,
            error.message,
            method,
            MODULE_NAME,
            data.roomId,
            data.user.id,
            { error }
        )
    }
}

async function handleRoomLeave(io: Server, socket: ServerSocket, data: LeaveRoomPayload) {
    const method = 'handleRoomLeave';
    try {
        const { roomId, user, isClient } = data;

        let room = await roomManager.getRoom(roomId);
        if (!room) {
            throw createSerErr(
                ErrorCode.ROOM_NOT_FOUND,
                `'Room: ${roomId} not found`,
                method,
                MODULE_NAME,
                roomId,
                user.id
            )
        }

        if (room.users.length === 2) {
            const otherUser = room.users.find((u: User) => u.id !== user.id);
            if (otherUser) {
                const adminId = otherUser.id;
                room = await roomManager.updateRoom(room, {
                    adminId: adminId
                });
            }

        } else if (room.users.length > 2 && room.adminId === user.id) {
            throw createSerErr(
                ErrorCode.ROOM_ADMIN_CANNOT_LEAVE,
                `Room: ${roomId} can not leave admin, please make another admin first`,
                'handleRoomLeave',
                MODULE_NAME,
                roomId,
                user.id
            )
        } else if (room.users.length === 1) {
            // 只剩下一个用户时，将房间设为非活跃
            room = await roomManager.updateRoom(room, {
                status: RoomActiveStatus.INACTIVE,
                adminId: room.users[0].id  // 将剩余用户设为管理员
            });
        } else {
            room = await roomManager.updateRoom(room, {
                status: RoomActiveStatus.INACTIVE,
                adminId: undefined
            });
        }

        // 用户离开房间
        room = await roomManager.leaveRoom(socket, room, user.id);

        const timestamp = getTimestamp();
        const rupl: RoomUpdatedPayload = {
            timestamp,
            roomId,
            users: room.users,
            lastActiveAt: timestamp,
            status: room.status,
            leavedUserId: isClient ? user.id : undefined,
            adminId: room.adminId
        }

        const ulpl: UserLeftPayload = {
            timestamp,
            roomId,
            leftUserId: user.id,
            isLeft: true,
        }
        emitPayload(socket, WebSocketEvents.ROOM.LEFT, ulpl);

        emitToRoom(
            io,
            roomId,
            WebSocketEvents.ROOM.UPDATE,
            rupl
        )

        // 如果用户是通过随机匹配进入的房间，离开时重新加入随机池
        const userExists = serverState.users.get(user.id);
        if (userExists && room.users.length === 0) {
            // 如果房间已空，且用户在线，重新加入随机池
            serverState.randomUser.set(user.id, {
                lastInviteTime: 0,
                isInviting: false
            });

            logger.info(`用户离开房间后重新加入随机池: ${user.id}`, {
                module: MODULE_NAME,
                method: 'handleRoomLeave',
                timestamp: getTimestamp(),
                details: {
                    randomPoolSize: serverState.randomUser.size
                }
            });
        }

    } catch (err) {
        const error = err instanceof Error ? err : new Error('Unknown error during room leave');
        emitErr(
            socket,
            ErrorCode.ROOM_LEAVE_FAILED,
            WebSocketEvents.ERROR.ROOM.LEAVE,
            error.message,
            method,
            MODULE_NAME,
            data.roomId,
            data.user.id,
            { error }
        )
    }
}

function findAvailableRandomUser(): User | undefined {
    const now = getTimestamp();
    for (const [userId, status] of serverState.randomUser.entries()) {
        // 如果用户不在邀请中，或者上次邀请已超时
        if ((!status.isInviting || (now - status.lastInviteTime > serConfig.chat.perInviteTimeout)) && serverState.users.get(userId)) {
            serverState.randomUser.set(userId, {
                lastInviteTime: now,
                isInviting: true
            });
            return serverState.users.get(userId);
        }
    }
    return undefined;
}

async function handleRoomJoin(io: Server, socket: ServerSocket, data: JoinRoomPayload) {
    const method = 'handleRoomJoin';
    try {
        let { roomId, user, reenter, userToken, chatMode, isInvitor } = data;
        const timestamp = getTimestamp();

        if (!userToken) userToken = genUserToken()

        let room: RoomWithStatus;
        // initial
        if (!roomId && !reenter) {
            room = await roomManager.createRoom(user.id, userToken, roomId);
            // join
        } else if (roomId && !reenter) {
            const existingRoom = await roomManager.getRoom(roomId);
            if (!existingRoom) {
                throw createSerErr(
                    ErrorCode.ROOM_NOT_FOUND,
                    `'Room: ${roomId} not found`,
                    method,
                    MODULE_NAME,
                    roomId,
                    user.id
                )
            }
            room = existingRoom;
            // reenter
        } else if (roomId && reenter) {
            const existingRoom = await roomManager.getRoom(roomId);
            if (!existingRoom) {
                room = await roomManager.createRoom(user.id, userToken, roomId);
            } else {
                room = existingRoom;
            }
        } else {
            throw createSerErr(
                ErrorCode.ROOM_INVALID_PARAMS,
                `Room not Found! Params Error: Room: ${roomId} , Reenter: ${reenter}`,
                method,
                MODULE_NAME,
                roomId,
                user.id
            )
        }

        if (room.users.length >= serConfig.room.roomMaxUsers) {
            throw createSerErr(
                ErrorCode.ROOM_FULL,
                `'Room: ${roomId} is full'`,
                'handleRoomJoin',
                MODULE_NAME,
                undefined,
                undefined,
                undefined,
                {
                    roomMaxUsers: serConfig.room.roomMaxUsers
                }
            )
        }

        // 检查用户是否被踢
        const isKicked = await roomManager.isUserKicked(room.id, user.id);
        if (isKicked) {
            throw createSerErr(
                ErrorCode.USER_BANNED,
                `User:${user.id} is banned from room:${room.id}`,
                method,
                MODULE_NAME,
                room.id,
                user.id
            )
        }

        const rupl: RoomUpdatedPayload = {
            timestamp,
            roomId: room.id,
            users: room.users,
            lastActiveAt: room.lastActiveAt,
            createdAt: room.createdAt,
            status: room.status,
            adminId: room.adminId,
            joinedUserId: user.id
        };

        if (room.users.find(u => u.id === user.id)) {
            emitPayload(socket, WebSocketEvents.ROOM.UPDATE, rupl);
            return;
        }

        // add user
        await userManager.addUser({ ...user, userToken } as UserWithStatus);
        // join room
        await roomManager.joinRoom(socket, room, user, userToken);
        // record user room state
        const lang = UserLangUtils.compact(user.lang);
        await userManager.addUserRoomState(user.id, room.id, { userToken, msgCount: 0, timestamp: getTimestamp(), lang });

        if (!serConfig.auth.enable) {
            socket.data.userId = user.id;
            socket.data.userName = user.name;
        }

        socket.data.roomId = room.id;
        socket.data.userToken = userToken;
        socket.data.lang = lang;

        rupl.lastActiveAt = timestamp;

        emitToRoom(
            io,
            room.id,
            WebSocketEvents.ROOM.UPDATE,
            rupl,
        )

        // 通知客户端房间已加入
        const jpl: UserJoinedPayload = {
            timestamp,
            joinedUserId: user.id,
            isJoin: true,
            userToken: userToken,
            room: {
                id: room.id,
                users: room.users,
                createdAt: room.createdAt,
                status: room.status,
                adminId: room.adminId
            } as Room
        }
        emitPayload(socket, WebSocketEvents.ROOM.JOINED, jpl);

        // 如果是随机模式, 从随机池中移除用户
        if (chatMode === EChatModeType.RANDOM) {
            // 移除当前用户和房间中的其他用户
            serverState.randomUser.delete(user.id);
            room.users.forEach(u => {
                if (u.id !== user.id) {
                    serverState.randomUser.delete(u.id);
                }
            });

            logger.info(`用户从随机池中移除: ${user.id}, 房间: ${room.id}`, {
                module: MODULE_NAME,
                method,
                timestamp: getTimestamp(),
                details: {
                    roomUserCount: room.users.length,
                    randomPoolSize: serverState.randomUser.size
                }
            });
        }
    } catch (err) {
        const serverErr = unknown__serErr(err, ErrorCode.ROOM_JOIN_FAILED);
        emitErr(
            socket,
            serverErr.code,
            WebSocketEvents.ERROR.ROOM.JOIN,
            serverErr.message,
            method,
            MODULE_NAME,
            data.roomId,
            data.user.id,
            serverErr.details
        )
    }
}

async function handleServerStatus(socket: ServerSocket, data: ClientPayload, callback: (ack: AckPayload) => void) {
    const method = 'handleServerStatus';

    let { userId } = data;
    const sspl: ServerStatusPayload = {
        timestamp: getTimestamp(),
        userId: data.userId,
        ack: EServerAckType.REJECT,
        population: serverState.users.size,
        randomModeCount: serverState.randomUser.size,
    };

    try {
        if (serverState.users.get(userId)) {
            sspl.ack = EServerAckType.CONFIRM;
            callback(sspl);
        }
    } catch (err) {
        const serverErr = unknown__serErr(err, ErrorCode.ROOM_JOIN_FAILED);
        emitErr(
            socket,
            serverErr.code,
            WebSocketEvents.ERROR.SERVER.STATUS,
            serverErr.message,
            method,
            MODULE_NAME,
            0,
            data.userId,
            serverErr.details
        );
        callback(sspl);
    }
}

async function handleSettings(socket: ServerSocket, data: ClientPayload, callback: (ack: AckPayload) => void) {
    const method = 'handleSettings';
    const apl: AckPayload = {
        ack: EServerAckType.REJECT,
        timestamp: getTimestamp()
    }

    try {
        let { userId, chatMode } = data;

        switch (chatMode) {
            case EChatModeType.RANDOM:
                if (serverState.users.get(userId)) {
                    serverState.randomUser.set(userId, {
                        lastInviteTime: getTimestamp(),
                        isInviting: false
                    })
                }
                apl.ack = EServerAckType.CONFIRM;
                callback(apl);
                break
            default:
                if (serverState.users.get(userId)) {
                    serverState.randomUser.delete(userId)
                }
                apl.ack = EServerAckType.CONFIRM;
                callback(apl);
        }

    } catch (err) {
        const serverErr = unknown__serErr(err, ErrorCode.ROOM_JOIN_FAILED);
        emitErr(
            socket,
            serverErr.code,
            WebSocketEvents.ERROR.SERVER.STATUS,
            serverErr.message,
            method,
            MODULE_NAME,
            0,
            data.userId,
            serverErr.details
        );
        callback(apl)
    }
}

/**
 * 处理随机模式
 * @param socket 
 * @param invitor 
 * @returns boolean
 */
async function handleRandom(socket: ServerSocket, invitor: User): Promise<{ isSuccess: boolean, targetSocket?: RemoteSocket<ServerEmitEvents, ServerSocketData> }> {
    const method = 'handleRandom';

    async function sendInvite(targetSocket: RemoteSocket<ServerEmitEvents, ServerSocketData>, user: User): Promise<boolean> {
        const ipl: InvitePayload = {
            timestamp: getTimestamp(),
            userId: user.id,
            userName: user.name,
            invitorLang: user.lang,
        };

        try {
            const inviteAck = await targetSocket.timeout(serConfig.chat.perInviteTimeout).emitWithAck(WebSocketEvents.ACTION.SERVER.INVITE, ipl)
            if (inviteAck.ack === EServerAckType.CONFIRM) {
                return true;
            }
            return false;
        } catch (err) {
            return false;
        }
    }

    try {
        // 判断模式, 如果是随机模式, 抽取用户并发送邀请
        for (let i = 0; i < serConfig.chat.randomInviteCount; i++) {
            // 1, 找到一个随机用户
            const randomUser = findAvailableRandomUser()
            if (randomUser) {
                // 2, 如果找到, 发送邀请
                const targetSocket = await findSocketByUserId(io, randomUser.id)
                if (targetSocket && await sendInvite(targetSocket, invitor)) {
                    // 3, 如果成功, 则return true
                    return { isSuccess: true, targetSocket };
                } else {
                    logger.info(`User: ${randomUser.id} is not available, skip invite`, {
                        module: MODULE_NAME,
                        timestamp: getTimestamp(),
                        method
                    })

                    // 3.1 如果失败, 则提示用户当前没有用户同意邀请
                    emitPayload(socket, WebSocketEvents.ACTION.SERVER.INVITE, {
                        timestamp: getTimestamp(),
                        userId: randomUser.id,
                        retry: i + 1
                    })

                    // 4, 如果失败, 则继续

                    continue;
                }
            }
        }

        // 3, 设置超时 使用emitWithAck, 否则, 如何同步请求与返回的状态?
        //      3.1 实现 server emit InvitePayload 并等待 ack
        //      3.2 如果超时, 则提示用户当前没有用户同意邀请
        // 4, 如果对方同意, 则继续,创建成功房间后, 发送房间号给目标用户, 并改为'join'
        // 5, 在 超时部分 处理重新开始#1, 循环5次, 如果5次都失败, 则提示用户当前没有用户同意邀请
        return { isSuccess: false, targetSocket: undefined };
    } catch (err) {
        const serverErr = unknown__serErr(err, ErrorCode.ROOM_MATCH_RANDOM_FAILED);
        emitErr(
            socket,
            serverErr.code,
            WebSocketEvents.ERROR.SERVER.MATCH_RANDOM,
            serverErr.message,
            method,
            MODULE_NAME,
            0,
            invitor.id,
            serverErr.details
        );
        return { isSuccess: false, targetSocket: undefined };
    }
}


/**
 * 用户切换为random后, 点击start, 触发服务端筛选随机用户, 并发送邀请.
 * 当随机用户同意邀请后, 通知用户房间号.
 * @param socket type of ServerSocket
 * @param data type of ClientPayload 
 */
async function startRandom(socket: ServerSocket, data: ClientPayload) {
    const method = 'startRandom';
    let { userId, chatMode, userToken } = data;
    const invitor = serverState.users.get(userId)
    if (!invitor) {
        emitErr(
            socket,
            ErrorCode.USER_NOT_EXISTS,
            WebSocketEvents.ERROR.USER.NOT_EXISTS,
            `User: ${userId} not exists`,
            method,
            MODULE_NAME,
            0,
            userId
        );
        return;
    }

    try {
        if (chatMode === EChatModeType.RANDOM) {
            // 邀请随机用户
            const { isSuccess, targetSocket } = await handleRandom(socket, invitor);
            if (isSuccess && targetSocket) {
                if (!userToken) userToken = genUserToken();
                const room = await roomManager.createRoom(invitor.id, userToken);

                // 通知邀请者房间号
                emitPayload(socket, WebSocketEvents.ROOM.RANDOM.ACK, {
                    timestamp: getTimestamp(),
                    ack: EServerAckType.CONFIRM,
                    roomId: room.id,
                    isInvitor: true,
                    userToken
                })

                // 为被邀请者生成独立的 userToken
                const inviteeToken = genUserToken();

                // 通知被邀请者房间号
                emitPayload(targetSocket, WebSocketEvents.ROOM.RANDOM.ACK, {
                    timestamp: getTimestamp(),
                    ack: EServerAckType.CONFIRM,
                    roomId: room.id,
                    isInvitor: false,
                    userToken: inviteeToken
                })
            }
        }
    } catch (err) {
        const serverErr = unknown__serErr(err, ErrorCode.ROOM_JOIN_FAILED);
        emitErr(
            socket,
            serverErr.code,
            WebSocketEvents.ERROR.SERVER.STATUS,
            serverErr.message,
            method,
            MODULE_NAME,
            0,
            data.userId,
            serverErr.details
        );
    }
}



export {
    serverState,
    roomManager,
    messageManager,
    userManager,

    initializeWebSocketIO,
    handleDisconnect
};
