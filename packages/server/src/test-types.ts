/**
 * 类型定义测试文件
 * 用于验证所有新增的类型定义是否正常工作
 */

// 导入枚举（作为值）
import {
  ServiceHealthStatus,
  RedisConnectionStatus,
  FallbackStrategy,
  ServiceType
} from '@/types/health.js';

// 导入接口（作为类型）
import type {
  BaseHealthCheckResult,
  ServiceHealthCheckResult,
  ConnectionMetrics,
  FallbackMetrics,
  CacheMetrics,
  PerformanceAnalysisResult
} from '@/types/health.js';

import type {
  RedisServiceHealthResult,
  RedisServiceStats,
  RedisConnectionMetrics,
  RedisHealthReport,
  RedisDiagnosticInfo
} from '@/types/redis-health.js';

import type {
  StatsSummary,
  DailyStats,
  CurrentHourStats,
  StatsTimestamps
} from '@/types/stats.js';

import type {
  LogHealthCheckResult
} from '@/types/logging.js';

/**
 * 测试健康状态枚举
 */
function testHealthStatusEnum(): void {
  const status: ServiceHealthStatus = ServiceHealthStatus.HEALTHY;
  console.log('Health status:', status);

  // 测试所有枚举值
  const allStatuses: ServiceHealthStatus[] = [
    ServiceHealthStatus.HEALTHY,
    ServiceHealthStatus.WARNING,
    ServiceHealthStatus.UNHEALTHY,
    ServiceHealthStatus.UNKNOWN,
    ServiceHealthStatus.ERROR
  ];

  console.log('All health statuses:', allStatuses);
}

/**
 * 测试 Redis 连接状态枚举
 */
function testRedisConnectionStatus(): void {
  const status: RedisConnectionStatus = RedisConnectionStatus.CONNECTED;
  console.log('Redis connection status:', status);

  // 测试所有枚举值
  const allStatuses: RedisConnectionStatus[] = [
    RedisConnectionStatus.DISCONNECTED,
    RedisConnectionStatus.CONNECTING,
    RedisConnectionStatus.CONNECTED,
    RedisConnectionStatus.RECONNECTING,
    RedisConnectionStatus.ERROR
  ];

  console.log('All Redis connection statuses:', allStatuses);
}

/**
 * 测试降级策略枚举
 */
function testFallbackStrategy(): void {
  const strategy: FallbackStrategy = FallbackStrategy.WRITE_THROUGH;
  console.log('Fallback strategy:', strategy);

  // 测试所有枚举值
  const allStrategies: FallbackStrategy[] = [
    FallbackStrategy.WRITE_THROUGH,
    FallbackStrategy.WRITE_BACK,
    FallbackStrategy.MEMORY_ONLY
  ];

  console.log('All fallback strategies:', allStrategies);
}

/**
 * 测试基础健康检查结果接口
 */
function testBaseHealthCheckResult(): BaseHealthCheckResult {
  return {
    status: ServiceHealthStatus.HEALTHY,
    timestamp: Date.now(),
    issues: [],
    recommendations: ['Keep monitoring']
  };
}

/**
 * 测试服务健康检查结果接口
 */
function testServiceHealthCheckResult(): ServiceHealthCheckResult {
  return {
    status: ServiceHealthStatus.HEALTHY,
    timestamp: Date.now(),
    issues: [],
    recommendations: [],
    serviceType: ServiceType.REDIS_CONNECTION,
    serviceName: 'Redis Connection Service',
    responseTime: 50
  };
}

/**
 * 测试连接指标接口
 */
function testConnectionMetrics(): ConnectionMetrics {
  return {
    totalConnections: 10,
    totalDisconnections: 2,
    totalErrors: 1,
    lastConnectedAt: Date.now() - 3600000,
    lastDisconnectedAt: Date.now() - 1800000,
    uptime: 3600000,
    status: RedisConnectionStatus.CONNECTED,
    reconnectAttempts: 0
  };
}

/**
 * 测试降级指标接口
 */
function testFallbackMetrics(): FallbackMetrics {
  return {
    memoryHits: 100,
    memoryMisses: 10,
    redisFailures: 2,
    fallbackActivations: 1,
    lastFallbackAt: Date.now() - 1800000,
    currentStrategy: FallbackStrategy.WRITE_THROUGH,
    isRedisAvailable: true,
    memoryStoreSize: 50,
    maxMemorySize: 1000
  };
}

/**
 * 测试缓存指标接口
 */
function testCacheMetrics(): CacheMetrics {
  return {
    localHits: 80,
    localMisses: 20,
    redisHits: 150,
    redisMisses: 30,
    evictions: 5,
    errors: 2,
    totalRequests: 287,
    localCacheSize: 100,
    hitRate: 80.14
  };
}

/**
 * 测试性能分析结果接口
 */
function testPerformanceAnalysisResult(): PerformanceAnalysisResult {
  return {
    summary: {
      totalRequests: 1000,
      totalHits: 800,
      totalErrors: 10,
      overallHitRate: 80.0,
      overallErrorRate: 1.0,
      activeCaches: 3
    },
    recommendations: [
      'Consider increasing cache TTL',
      'Monitor error patterns'
    ],
    issues: [
      'High miss rate on user cache'
    ]
  };
}

/**
 * 测试 Redis 服务健康结果接口
 */
function testRedisServiceHealthResult(): RedisServiceHealthResult {
  return {
    isHealthy: true,
    services: {
      connection: true,
      room: true,
      user: true,
      stats: true
    }
  };
}

/**
 * 测试 Redis 服务统计接口
 */
function testRedisServiceStats(): RedisServiceStats {
  return {
    rooms: 25,
    users: 150,
    statsTimestamps: {
      get: 48,
      post: 24
    },
    memory: '45.2 MB'
  };
}

/**
 * 测试 Redis 健康报告接口
 */
function testRedisHealthReport(): RedisHealthReport {
  return {
    overall: {
      status: ServiceHealthStatus.HEALTHY,
      isRedisAvailable: true,
      connectionStatus: RedisConnectionStatus.CONNECTED,
      fallbackActive: false
    },
    connection: testConnectionMetrics() as RedisConnectionMetrics,
    services: testRedisServiceHealthResult(),
    stats: testRedisServiceStats(),
    fallback: testFallbackMetrics(),
    timestamp: Date.now()
  };
}

/**
 * 测试统计摘要接口
 */
function testStatsSummary(): StatsSummary {
  return {
    totalGetRequests: 1000,
    totalPostRequests: 500,
    uniquePaths: new Set(['/api/health', '/api/stats', '/api/rooms']),
    timeRange: {
      start: Date.now() - 86400000,
      end: Date.now()
    },
    topPaths: [
      { path: '/api/health', count: 300, method: 'GET' },
      { path: '/api/rooms', count: 200, method: 'POST' }
    ],
    generatedAt: Date.now()
  };
}

/**
 * 测试每日统计接口
 */
function testDailyStats(): DailyStats {
  return {
    get: new Map([
      ['/api/health', 100],
      ['/api/stats', 50]
    ]),
    post: new Map([
      ['/api/rooms', 30],
      ['/api/users', 20]
    ]),
    date: '2024-01-15',
    totalRequests: 200,
    uniquePathsCount: 4
  };
}

/**
 * 测试当前小时统计接口
 */
function testCurrentHourStats(): CurrentHourStats {
  return {
    get: new Map([
      ['/api/health', 10],
      ['/api/stats', 5]
    ]),
    post: new Map([
      ['/api/rooms', 3],
      ['/api/users', 2]
    ]),
    hour: Math.floor(Date.now() / 1000 / 3600) * 3600,
    lastUpdated: Date.now()
  };
}

/**
 * 测试统计时间戳接口
 */
function testStatsTimestamps(): StatsTimestamps {
  return {
    get: [**********, **********, **********],
    post: [**********, **********]
  };
}

/**
 * 测试日志健康检查结果接口
 */
function testLogHealthCheckResult(): LogHealthCheckResult {
  return {
    status: ServiceHealthStatus.HEALTHY,
    totalEntries: 1000,
    entriesByLevel: {
      DEBUG: 500,
      INFO: 300,
      WARN: 150,
      ERROR: 50
    } as any,
    oldestEntry: Date.now() - 86400000,
    newestEntry: Date.now(),
    storageUsage: {
      totalBytes: 1048576,
      byLevel: {
        DEBUG: 524288,
        INFO: 262144,
        WARN: 131072,
        ERROR: 131072
      } as any
    },
    performance: {
      averageWriteTime: 5,
      averageReadTime: 2,
      errorRate: 0.05
    },
    issues: [],
    recommendations: ['Consider log rotation']
  };
}

/**
 * 运行所有测试
 */
export function runTypeTests(): void {
  console.log('🧪 开始类型定义测试...\n');

  try {
    console.log('✅ 测试健康状态枚举');
    testHealthStatusEnum();

    console.log('✅ 测试 Redis 连接状态枚举');
    testRedisConnectionStatus();

    console.log('✅ 测试降级策略枚举');
    testFallbackStrategy();

    console.log('✅ 测试基础健康检查结果');
    const baseHealth = testBaseHealthCheckResult();
    console.log('Base health result:', baseHealth);

    console.log('✅ 测试服务健康检查结果');
    const serviceHealth = testServiceHealthCheckResult();
    console.log('Service health result:', serviceHealth);

    console.log('✅ 测试连接指标');
    const connectionMetrics = testConnectionMetrics();
    console.log('Connection metrics:', connectionMetrics);

    console.log('✅ 测试降级指标');
    const fallbackMetrics = testFallbackMetrics();
    console.log('Fallback metrics:', fallbackMetrics);

    console.log('✅ 测试缓存指标');
    const cacheMetrics = testCacheMetrics();
    console.log('Cache metrics:', cacheMetrics);

    console.log('✅ 测试性能分析结果');
    const performanceAnalysis = testPerformanceAnalysisResult();
    console.log('Performance analysis:', performanceAnalysis);

    console.log('✅ 测试 Redis 健康报告');
    const redisHealth = testRedisHealthReport();
    console.log('Redis health report:', redisHealth);

    console.log('✅ 测试统计摘要');
    const statsSummary = testStatsSummary();
    console.log('Stats summary:', statsSummary);

    console.log('✅ 测试每日统计');
    const dailyStats = testDailyStats();
    console.log('Daily stats:', dailyStats);

    console.log('✅ 测试当前小时统计');
    const currentHourStats = testCurrentHourStats();
    console.log('Current hour stats:', currentHourStats);

    console.log('✅ 测试统计时间戳');
    const statsTimestamps = testStatsTimestamps();
    console.log('Stats timestamps:', statsTimestamps);

    console.log('✅ 测试日志健康检查结果');
    const logHealth = testLogHealthCheckResult();
    console.log('Log health result:', logHealth);

    console.log('\n🎉 所有类型定义测试通过！');
    console.log('✨ IDE 智能提示应该能够正常工作');
    console.log('🔧 开发者现在可以清楚地了解所有返回值的结构和可能的值');

  } catch (error) {
    console.error('❌ 类型定义测试失败:', error);
    throw error;
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runTypeTests();
}
