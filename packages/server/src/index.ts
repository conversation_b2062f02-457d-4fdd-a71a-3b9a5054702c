import express from 'express';
import type { Express } from 'express';
import { createServer } from 'http';
import { serConfig } from '@/configs/index.js';
import { logger, getTimestamp } from '@unibabble/shared';
import { initializeWebSocketIO } from '@/ws.js';
import {
    startRoomStatusTimer,
    startUserStatusTimer,
    showServerStateStats,
    startRedisCleanupTimer,
    startRedisHealthCheckTimer,
    startPerformanceMonitorTimer,
    startLogCleanupTimer
} from '@/timers.js';
import { roomRoutes, translateRoutes, cacheRoutes, redisHealthRoutes } from '@/routes/index.js';
import logRoutes from '@/routes/logRoutes.js';
import { requestLogMiddleware, errorLogMiddleware } from '@/middleware/logMiddleware.js';
import cors from 'cors';
import type { ServerState, UserInviteStatus } from './types/index.js';
import { errorHandler, googleAuthMiddleware } from '@/middleware/index.js';
import { statsMiddleware, createStatsRoutes } from '@/middleware/statsMiddleware.js';
import { cacheOptimizer } from '@/utils/cacheOptimizer.js';


const MODULE_NAME = 'Server:index';

export const serverState: ServerState = {
    roomStates: new Map(), // 保留用于向后兼容，但实际使用 Redis
    userRoomMsgState: new Map(), // 保留用于向后兼容，但实际使用 Redis
    users: new Map(), // 保留用于向后兼容，但实际使用 Redis
    randomUser: new Map<string, UserInviteStatus>(), // 已部分迁移到 Redis
    routeGetStatistic: new Map(), // 已迁移到 Redis，保留用于向后兼容
    routePostStatistic: new Map() // 已迁移到 Redis，保留用于向后兼容
};


export const app: Express = express();
const httpServer = createServer(app);
export const io = initializeWebSocketIO(httpServer);

logger.debug(`process.env.SERVER_AUTH_ENABLE type: ${typeof process.env.SERVER_AUTH_ENABLE}`, {
    module: MODULE_NAME,
    method: 'start',
    timestamp: getTimestamp()
});
logger.debug(`process.env.SERVER_AUTH_ENABLE: ${process.env.SERVER_AUTH_ENABLE}`, {
    module: MODULE_NAME,
    method: 'start',
    timestamp: getTimestamp()
});
logger.debug(`serConfig: ${JSON.stringify(serConfig, null, 2)}`, {
    module: MODULE_NAME,
    method: 'start',
    timestamp: getTimestamp()
});

// middleware
app.use(cors(serConfig.cors)); // 允许跨域请求
app.use(express.json()); // 解析 JSON 请求体
app.use(requestLogMiddleware); // 请求日志中间件
app.use(statsMiddleware); // 统计中间件
if (serConfig.auth.enable) {
    io.use(googleAuthMiddleware);
}
app.use(errorLogMiddleware); // 错误日志中间件
app.use(errorHandler); // 错误处理中间件

// routes
app.use('/api/room', roomRoutes);
app.use('/api/translate', translateRoutes);
app.use('/api/stats', createStatsRoutes()); // 统计数据路由
app.use('/api/cache', cacheRoutes); // 缓存管理路由
app.use('/api/redis', redisHealthRoutes); // Redis 健康监控路由
app.use('/api/logs', logRoutes); // 日志管理路由


const port = serConfig.server.port;
httpServer.listen(port, () => {
    logger.info(`Server is running on port ${port}`, {
        module: MODULE_NAME,
        method: 'start',
        timestamp: getTimestamp()
    });
});

// timers
startRoomStatusTimer(io);
startUserStatusTimer(io);
startRedisCleanupTimer();
startRedisHealthCheckTimer();
startPerformanceMonitorTimer();
startLogCleanupTimer(); // 启动日志清理定时器
showServerStateStats();

// 启动缓存优化器
cacheOptimizer.start();