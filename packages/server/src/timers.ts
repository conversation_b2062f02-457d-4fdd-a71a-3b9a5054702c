import { serConfig } from "@/configs/index.js";
import { roomManager, serverState, userManager } from "@/ws.js";
import {
  getTimestamp, createSystemMessagePayload, logger,
  WebSocketEvents, RoomActiveStatus, unknown__serErr, findSocketByUserId
} from '@unibabble/shared'
import type { TranslateLang, RoomUpdatedPayload, ServerSocket } from "@unibabble/shared";
import type { Server } from 'socket.io';
import { emitToRoom } from "./utils/reponse.js";
import { roomManager as redisRoomManager } from '@/managers/roomManager.js';
import { userManager as redisUserManager } from '@/managers/userManager.js';
import { statsManager } from '@/managers/statsManager.js';
import { executeWithDistributedLock } from '@/utils/distributedLock.js';
import axios from 'axios';


// 存储所有定时器的引用
const intervals: NodeJS.Timeout[] = [];

const MODULE_NAME = 'Server:timer'

logger.info(`'最大空闲时间: ${serConfig.room.roomMaxIdle} ms, 空闲房间扫描间隔: ${serConfig.room.roomMaxIdleCheckInterval} ms, 强制离线间隔: ${serConfig.room.roomForceDisconnectInterval} ms'`, {
  module: MODULE_NAME,
  timestamp: getTimestamp()
})

/* 
房间长时间静置, 检查是否有用户在线, 如果没有, 清理房间, 使用户下线, 清理userMessageRates
*/
// 房间状态检查定时器
export function startRoomStatusTimer(io: Server) {
  const checkRoomStatus = async () => {
    // 使用分布式锁确保只有一个实例执行房间状态检查
    const result = await executeWithDistributedLock(
      'room-status-check',
      async () => {
        try {
          const roomIds = await redisRoomManager.getAllRoomIds();
          const timestamp = getTimestamp();
          const warnLine = timestamp - serConfig.room.roomMaxIdle;
          const deadLine = timestamp - serConfig.room.roomMaxIdle - serConfig.room.roomForceDisconnectInterval;

          let processedRooms = 0;
          let closedRooms = 0;
          let warnedRooms = 0;

          for (const roomId of roomIds) {
            const room = await redisRoomManager.getRoom(roomId);
            if (!room) continue;

            processedRooms++;

            if (room.lastActiveAt <= deadLine && room.status === RoomActiveStatus.IDLE) {
              const sysMsg = createSystemMessagePayload(
                `The room is closed, You can re-enter.`,
                roomId
              );
              emitToRoom(io, roomId, WebSocketEvents.ACTION.SERVER.FORCEDISCONNECT, sysMsg);

              // 处理房间中的用户
              for (const user of room.users) {
                const socket = await findSocketByUserId(io, user.id, roomId);
                if (socket) {
                  await socket.leave(roomId.toString());
                }
                await redisUserManager.deleteUser(user.id);
              }

              await redisRoomManager.deleteRoom(roomId);
              closedRooms++;
            } else if (room.lastActiveAt <= warnLine && room.lastActiveAt > deadLine) {
              // 即将超时，发送警告
              const content = `The room is about to close due to IDLE more than ${serConfig.room.roomMaxIdle / 1000 / 60} minutes`;
              io.to(roomId.toString()).emit(WebSocketEvents.WARNNING.ROOM.IDLE, createSystemMessagePayload(content, roomId));

              await redisRoomManager.updateRoomStatus(roomId, RoomActiveStatus.IDLE);
              warnedRooms++;
            }
          }

          logger.debug('Room status check completed', {
            module: MODULE_NAME,
            timestamp: getTimestamp(),
            details: {
              processedRooms,
              closedRooms,
              warnedRooms
            }
          });

          return { processedRooms, closedRooms, warnedRooms };
        } catch (error) {
          logger.error('Room status check failed', {
            module: MODULE_NAME,
            timestamp: getTimestamp(),
            error: error instanceof Error ? error.message : 'Unknown error'
          });
          throw error;
        }
      },
      serConfig.room.roomMaxIdleCheckInterval // 锁的 TTL 设置为检查间隔时间
    );

    if (result === null) {
      logger.debug('Room status check skipped - another instance is processing', {
        module: MODULE_NAME,
        timestamp: getTimestamp()
      });
    }
  };

  const interval = setInterval(checkRoomStatus, serConfig.room.roomMaxIdleCheckInterval);
  intervals.push(interval);
  return interval;
}


/* 
如果用户断连, 短时间无法再次连接, 或者是客户端的transport error, 这样, 服务器如何识别这个用户的状态, 
用户断网: 当可连时..., 服务端扫描失效, 清理房间, 间隔?
当前客户端ws连接失败: 当reconnect failed时通过api请求退出room,方便重新连接并join - ok

*/
export function startUserStatusTimer(io: Server) {
  const checkUserStatus = async () => {
    // 使用分布式锁确保只有一个实例执行用户状态检查
    const result = await executeWithDistributedLock(
      'user-status-check',
      async () => {
        try {
          // online sockets
          const onlineUserIds = Array.from(io.sockets.sockets.values())
            .map((socket: ServerSocket) => socket.data.userId)
            .filter(Boolean);

          // recorded ID from Redis
          const registeredUserIds = await redisUserManager.getAllUserIds();

          // unavailable userID
          const invalidUserIds = registeredUserIds.filter(id => !onlineUserIds.includes(id));

          let processedUsers = 0;
          let cleanedUsers = 0;
          let updatedRooms = 0;

          // clean unavailable userID
          for (const userId of invalidUserIds) {
            processedUsers++;
            const userRoomIds = await redisUserManager.getUserRoomIds(userId);

            for (const roomId of userRoomIds) {
              const room = await redisRoomManager.getRoom(roomId);

              if (room && room.status !== RoomActiveStatus.IDLE) {
                // 从房间移除用户
                await redisRoomManager.removeUserFromRoom(roomId, userId);

                const socket = await findSocketByUserId(io, userId, roomId);
                if (socket) {
                  await socket.leave(roomId.toString());
                }

                // 获取更新后的房间信息
                const updatedRoom = await redisRoomManager.getRoom(roomId);
                if (updatedRoom) {
                  // emit to room
                  const rupl: RoomUpdatedPayload = {
                    timestamp: getTimestamp(),
                    roomId: updatedRoom.id,
                    users: updatedRoom.users,
                    status: updatedRoom.status,
                    lastActiveAt: updatedRoom.lastActiveAt,
                    createdAt: updatedRoom.createdAt
                  };
                  emitToRoom(io, updatedRoom.id, WebSocketEvents.ROOM.UPDATE, rupl);
                  updatedRooms++;
                }
              }
            }

            await redisUserManager.deleteUser(userId);
            cleanedUsers++;
          }

          logger.debug('User status check completed', {
            module: MODULE_NAME,
            timestamp: getTimestamp(),
            details: {
              onlineUsers: onlineUserIds.length,
              registeredUsers: registeredUserIds.length,
              processedUsers,
              cleanedUsers,
              updatedRooms
            }
          });

          return { processedUsers, cleanedUsers, updatedRooms };
        } catch (error) {
          logger.error('User status check failed', {
            module: MODULE_NAME,
            timestamp: getTimestamp(),
            error: error instanceof Error ? error.message : 'Unknown error'
          });
          throw error;
        }
      },
      serConfig.room.roomUserStatusCheckInterval // 锁的 TTL 设置为检查间隔时间
    );

    if (result === null) {
      logger.debug('User status check skipped - another instance is processing', {
        module: MODULE_NAME,
        timestamp: getTimestamp()
      });
    }
  };

  const interval = setInterval(checkUserStatus, serConfig.room.roomUserStatusCheckInterval);
  intervals.push(interval);
  return interval;
}

// Redis 数据清理定时器
export function startRedisCleanupTimer() {
  const cleanupRedisData = async () => {
    // 使用分布式锁确保只有一个实例执行数据清理
    const result = await executeWithDistributedLock(
      'redis-data-cleanup',
      async () => {
        try {
          logger.info('Starting Redis data cleanup', {
            module: MODULE_NAME,
            timestamp: getTimestamp()
          });

          // 清理过期统计数据
          const expiredStatsCount = await statsManager.cleanupExpiredStats();

          // 清理过期用户房间状态
          const userIds = await redisUserManager.getAllUserIds();
          let expiredUserStatesCount = 0;

          for (const userId of userIds) {
            const cleaned = await redisUserManager.cleanupExpiredUserRoomStates(userId);
            expiredUserStatesCount += cleaned;
          }

          // 清理空房间
          const emptyRoomsCount = await redisRoomManager.cleanEmptyRooms();

          // 清理非活跃用户（超过24小时没有活动）
          const maxInactiveTime = 24 * 60 * 60 * 1000; // 24小时
          const inactiveUsersCount = await redisUserManager.cleanupInactiveUsers(maxInactiveTime);

          const totalCleaned = expiredStatsCount + expiredUserStatesCount + emptyRoomsCount + inactiveUsersCount;

          logger.info('Redis data cleanup completed', {
            module: MODULE_NAME,
            timestamp: getTimestamp(),
            details: {
              expiredStats: expiredStatsCount,
              expiredUserStates: expiredUserStatesCount,
              emptyRooms: emptyRoomsCount,
              inactiveUsers: inactiveUsersCount,
              totalCleaned
            }
          });

          return {
            expiredStats: expiredStatsCount,
            expiredUserStates: expiredUserStatesCount,
            emptyRooms: emptyRoomsCount,
            inactiveUsers: inactiveUsersCount,
            totalCleaned
          };
        } catch (error) {
          logger.error('Redis data cleanup failed', {
            module: MODULE_NAME,
            timestamp: getTimestamp(),
            error: error instanceof Error ? error.message : 'Unknown error'
          });
          throw error;
        }
      },
      5 * 60 * 1000 // 5分钟锁过期时间，足够完成清理任务
    );

    if (result === null) {
      logger.debug('Redis cleanup skipped - another instance is processing', {
        module: MODULE_NAME,
        timestamp: getTimestamp()
      });
    }
  };

  // 每小时执行一次清理
  const interval = setInterval(cleanupRedisData, 60 * 60 * 1000);
  intervals.push(interval);

  // 启动时延迟30秒执行一次清理，避免多实例同时启动时的竞争
  setTimeout(cleanupRedisData, 30 * 1000);

  return interval;
}

// Redis 健康检查定时器
export function startRedisHealthCheckTimer() {
  const checkRedisHealth = async () => {
    try {
      const { checkRedisHealth, getRedisServiceStats } = await import('@/services/redis/index.js');

      // 检查 Redis 连接健康状态
      const healthStatus = await checkRedisHealth();

      if (!healthStatus.isHealthy) {
        logger.error('Redis health check failed', {
          module: MODULE_NAME,
          timestamp: getTimestamp(),
          details: {
            services: healthStatus.services,
            error: healthStatus.error
          }
        });
        return;
      }

      // 获取 Redis 服务统计信息
      const stats = await getRedisServiceStats();

      logger.debug('Redis health check passed', {
        module: MODULE_NAME,
        timestamp: getTimestamp(),
        details: {
          health: healthStatus.services,
          stats
        }
      });

      // 如果内存使用过高，记录警告
      if (stats.memory) {
        const memoryMB = parseFloat(stats.memory.replace(' MB', ''));
        if (memoryMB > 500) { // 500MB 警告阈值
          logger.warn('Redis memory usage is high', {
            module: MODULE_NAME,
            timestamp: getTimestamp(),
            details: {
              memory: stats.memory,
              threshold: '500 MB'
            }
          });
        }
      }

    } catch (error) {
      logger.error('Redis health check error', {
        module: MODULE_NAME,
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  // 每5分钟检查一次 Redis 健康状态
  const interval = setInterval(checkRedisHealth, 5 * 60 * 1000);
  intervals.push(interval);

  // 启动时立即检查一次
  checkRedisHealth();

  return interval;
}

// 性能监控定时器
export function startPerformanceMonitorTimer() {
  const monitorPerformance = async () => {
    try {
      const [roomStats, userStats, apiStats] = await Promise.all([
        redisRoomManager.getRoomStats(),
        redisUserManager.getUserStats(),
        statsManager.getStatsSummary()
      ]);

      // 检查性能指标
      const performanceMetrics = {
        rooms: roomStats,
        users: userStats,
        api: {
          totalRequests: apiStats.totalGetRequests + apiStats.totalPostRequests,
          uniquePaths: apiStats.uniquePaths.size,
          timeRange: apiStats.timeRange
        },
        memory: process.memoryUsage(),
        uptime: process.uptime()
      };

      // 记录性能指标
      logger.info('Performance metrics', {
        module: MODULE_NAME,
        timestamp: getTimestamp(),
        details: performanceMetrics
      });

      // 检查是否有性能问题
      if (roomStats.total > 1000) {
        logger.warn('High number of rooms detected', {
          module: MODULE_NAME,
          timestamp: getTimestamp(),
          details: { totalRooms: roomStats.total }
        });
      }

      if (userStats.total > 5000) {
        logger.warn('High number of users detected', {
          module: MODULE_NAME,
          timestamp: getTimestamp(),
          details: { totalUsers: userStats.total }
        });
      }

      // 检查内存使用
      const memoryUsage = process.memoryUsage();
      const memoryUsageMB = memoryUsage.heapUsed / 1024 / 1024;
      if (memoryUsageMB > 512) { // 512MB 警告阈值
        logger.warn('High memory usage detected', {
          module: MODULE_NAME,
          timestamp: getTimestamp(),
          details: {
            heapUsed: `${Math.round(memoryUsageMB)} MB`,
            heapTotal: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)} MB`
          }
        });
      }

    } catch (error) {
      logger.error('Performance monitoring failed', {
        module: MODULE_NAME,
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  // 每10分钟监控一次性能
  const interval = setInterval(monitorPerformance, 10 * 60 * 1000);
  intervals.push(interval);

  // 启动时延迟1分钟执行一次
  setTimeout(monitorPerformance, 60 * 1000);

  return interval;
}


export function showServerStateStats() {
  // 对serverState数据进行统计并显示日志
  const checkServerState = async () => {
    try {
      // 使用 Redis-based 管理器获取统计数据
      const [roomStats, userStats, apiStats] = await Promise.all([
        redisRoomManager.getRoomStats(),
        redisUserManager.getUserStats(),
        statsManager.getStatsSummary()
      ]);

      const stats = {
        // 统计房间数据
        rooms: roomStats,
        // 统计用户数据
        users: userStats,
        // API 统计数据
        api: {
          totalGetRequests: apiStats.totalGetRequests,
          totalPostRequests: apiStats.totalPostRequests,
          uniquePaths: apiStats.uniquePaths.size,
          timeRange: apiStats.timeRange
        },
        // 保留随机用户统计（尚未完全迁移）
        randomUsers: serverState.randomUser.size,
        // 系统信息
        system: {
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          pid: process.pid
        }
      };

      logger.info('Server State Statistics (Redis-based)', {
        module: MODULE_NAME,
        timestamp: getTimestamp(),
        details: stats
      });
    } catch (error) {
      logger.error('Failed to get server state statistics', {
        module: MODULE_NAME,
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  const interval = setInterval(checkServerState, 30000); // 改为30秒间隔，减少日志频率
  intervals.push(interval);
  return interval;
}

/**
 * 启动日志清理定时器
 */
export function startLogCleanupTimer() {
  const cleanupLogs = async () => {
    // 使用分布式锁确保只有一个实例执行日志清理
    const result = await executeWithDistributedLock(
      'log-cleanup-task',
      async () => {
        try {
          const { logManager } = await import('@/managers/logManager.js');
          const cleanupResult = await logManager.cleanup();

          logger.info('Log cleanup completed', {
            module: MODULE_NAME,
            method: 'cleanupLogs',
            timestamp: getTimestamp(),
            details: {
              deletedEntries: cleanupResult.deletedEntries,
              deletedBytes: cleanupResult.deletedBytes,
              duration: cleanupResult.duration,
              processedLevels: cleanupResult.processedLevels,
              errors: cleanupResult.errors.length
            }
          });

          return cleanupResult;
        } catch (error) {
          logger.error('Log cleanup failed', {
            module: MODULE_NAME,
            method: 'cleanupLogs',
            timestamp: getTimestamp(),
            error: error instanceof Error ? error.message : 'Unknown error'
          });
          throw error;
        }
      },
      60000 // 60秒锁过期时间
    );

    if (!result.success) {
      logger.warn('Log cleanup skipped - another instance is running', {
        module: MODULE_NAME,
        method: 'cleanupLogs',
        timestamp: getTimestamp()
      });
    }
  };

  // 每小时执行一次日志清理
  const interval = setInterval(cleanupLogs, 60 * 60 * 1000);
  intervals.push(interval);

  logger.info('Log cleanup timer started', {
    module: MODULE_NAME,
    method: 'startLogCleanupTimer',
    timestamp: getTimestamp(),
    details: { interval: '1 hour' }
  });

  return interval;
}


// 清理所有定时器
export function clearAllTimers() {
  intervals.forEach(interval => clearInterval(interval));
  intervals.length = 0;
}

// 注册进程退出处理
process.on('SIGTERM', clearAllTimers);
process.on('SIGINT', clearAllTimers); 