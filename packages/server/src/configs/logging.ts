/**
 * Redis 日志系统配置
 */

export interface LoggingConfig {
  // 基础配置
  enabled: boolean;
  namespace: string;
  
  // 日志级别配置
  levels: {
    DEBUG: LogLevelConfig;
    INFO: LogLevelConfig;
    WARN: LogLevelConfig;
    ERROR: LogLevelConfig;
  };
  
  // 批量写入配置
  batch: {
    size: number;
    flushInterval: number;
    maxRetries: number;
  };
  
  // 性能配置
  performance: {
    maxConcurrentWrites: number;
    compressionEnabled: boolean;
    indexingEnabled: boolean;
  };
  
  // 清理配置
  cleanup: {
    enabled: boolean;
    interval: number;
    maxEntriesPerCleanup: number;
  };
}

export interface LogLevelConfig {
  enabled: boolean;
  retentionDays: number;
  maxEntriesPerHour: number;
  compressionThreshold: number;
}

// 默认配置
const defaultConfig: LoggingConfig = {
  enabled: true,
  namespace: 'logs',
  
  levels: {
    DEBUG: {
      enabled: process.env.NODE_ENV === 'development',
      retentionDays: 1,
      maxEntriesPerHour: 10000,
      compressionThreshold: 1000
    },
    INFO: {
      enabled: true,
      retentionDays: 7,
      maxEntriesPerHour: 5000,
      compressionThreshold: 500
    },
    WARN: {
      enabled: true,
      retentionDays: 30,
      maxEntriesPerHour: 1000,
      compressionThreshold: 100
    },
    ERROR: {
      enabled: true,
      retentionDays: 90,
      maxEntriesPerHour: 500,
      compressionThreshold: 50
    }
  },
  
  batch: {
    size: parseInt(process.env.LOG_BATCH_SIZE || '50'),
    flushInterval: parseInt(process.env.LOG_FLUSH_INTERVAL || '5000'),
    maxRetries: 3
  },
  
  performance: {
    maxConcurrentWrites: 10,
    compressionEnabled: process.env.NODE_ENV === 'production',
    indexingEnabled: true
  },
  
  cleanup: {
    enabled: true,
    interval: 60 * 60 * 1000, // 1小时
    maxEntriesPerCleanup: 1000
  }
};

// 环境变量覆盖配置
export const loggingConfig: LoggingConfig = {
  ...defaultConfig,
  enabled: process.env.REDIS_LOGGING_ENABLED !== 'false',
  
  levels: {
    DEBUG: {
      ...defaultConfig.levels.DEBUG,
      enabled: process.env.LOG_DEBUG_ENABLED !== 'false' && process.env.NODE_ENV === 'development',
      retentionDays: parseInt(process.env.LOG_DEBUG_RETENTION_DAYS || '1')
    },
    INFO: {
      ...defaultConfig.levels.INFO,
      enabled: process.env.LOG_INFO_ENABLED !== 'false',
      retentionDays: parseInt(process.env.LOG_INFO_RETENTION_DAYS || '7')
    },
    WARN: {
      ...defaultConfig.levels.WARN,
      enabled: process.env.LOG_WARN_ENABLED !== 'false',
      retentionDays: parseInt(process.env.LOG_WARN_RETENTION_DAYS || '30')
    },
    ERROR: {
      ...defaultConfig.levels.ERROR,
      enabled: process.env.LOG_ERROR_ENABLED !== 'false',
      retentionDays: parseInt(process.env.LOG_ERROR_RETENTION_DAYS || '90')
    }
  }
};

// 日志级别枚举
export enum LogLevel {
  DEBUG = 'DEBUG',
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR'
}

// 日志级别优先级
export const LOG_LEVEL_PRIORITY = {
  [LogLevel.DEBUG]: 0,
  [LogLevel.INFO]: 1,
  [LogLevel.WARN]: 2,
  [LogLevel.ERROR]: 3
};

// 验证配置
export const validateLoggingConfig = (config: LoggingConfig): string[] => {
  const errors: string[] = [];
  
  if (config.batch.size <= 0) {
    errors.push('Batch size must be greater than 0');
  }
  
  if (config.batch.flushInterval <= 0) {
    errors.push('Flush interval must be greater than 0');
  }
  
  Object.entries(config.levels).forEach(([level, levelConfig]) => {
    if (levelConfig.retentionDays <= 0) {
      errors.push(`${level} retention days must be greater than 0`);
    }
    
    if (levelConfig.maxEntriesPerHour <= 0) {
      errors.push(`${level} max entries per hour must be greater than 0`);
    }
  });
  
  return errors;
};

// 获取日志级别配置
export const getLogLevelConfig = (level: LogLevel): LogLevelConfig => {
  return loggingConfig.levels[level];
};

// 检查日志级别是否启用
export const isLogLevelEnabled = (level: LogLevel): boolean => {
  return loggingConfig.enabled && loggingConfig.levels[level].enabled;
};

export default loggingConfig;
