import { statsService } from '@/services/redis/index.js';
import type {
  StatsSummary,
  DailyStats,
  CurrentHourStats,
  StatsTimestamps
} from '@/types/stats.js';

/**
 * Redis-based Statistics Manager
 * 使用 Redis 存储 API 访问统计数据
 */
export const statsManager = {
  /**
   * 记录 GET 请求统计
   */
  async recordGetRequest(path: string, timestamp?: number): Promise<void> {
    await statsService.recordGetRequest(path, timestamp);
  },

  /**
   * 记录 POST 请求统计
   */
  async recordPostRequest(path: string, timestamp?: number): Promise<void> {
    await statsService.recordPostRequest(path, timestamp);
  },

  /**
   * 批量记录 GET 请求统计
   */
  async recordGetRequests(requests: Array<{ path: string; timestamp?: number }>): Promise<void> {
    await statsService.recordGetRequests(requests);
  },

  /**
   * 批量记录 POST 请求统计
   */
  async recordPostRequests(requests: Array<{ path: string; timestamp?: number }>): Promise<void> {
    await statsService.recordPostRequests(requests);
  },

  /**
   * 获取 GET 请求统计
   */
  async getGetStats(timestamp: number): Promise<Map<string, number>> {
    return await statsService.getGetStats(timestamp);
  },

  /**
   * 获取 POST 请求统计
   */
  async getPostStats(timestamp: number): Promise<Map<string, number>> {
    return await statsService.getPostStats(timestamp);
  },

  /**
   * 获取指定路径的 GET 请求统计
   */
  async getGetStatsForPath(path: string, timestamp: number): Promise<number> {
    return await statsService.getGetStatsForPath(path, timestamp);
  },

  /**
   * 获取指定路径的 POST 请求统计
   */
  async getPostStatsForPath(path: string, timestamp: number): Promise<number> {
    return await statsService.getPostStatsForPath(path, timestamp);
  },

  /**
   * 获取时间范围内的 GET 请求统计
   */
  async getGetStatsRange(startTimestamp: number, endTimestamp: number): Promise<Map<number, Map<string, number>>> {
    return await statsService.getGetStatsRange(startTimestamp, endTimestamp);
  },

  /**
   * 获取时间范围内的 POST 请求统计
   */
  async getPostStatsRange(startTimestamp: number, endTimestamp: number): Promise<Map<number, Map<string, number>>> {
    return await statsService.getPostStatsRange(startTimestamp, endTimestamp);
  },

  /**
   * 删除指定时间戳的统计数据
   */
  async deleteStats(timestamp: number): Promise<boolean> {
    return await statsService.deleteStats(timestamp);
  },

  /**
   * 清理过期的统计数据
   */
  async cleanupExpiredStats(): Promise<number> {
    return await statsService.cleanupExpiredStats();
  },

  /**
   * 获取所有统计时间戳
   */
  async getAllStatsTimestamps(): Promise<StatsTimestamps> {
    return await statsService.getAllStatsTimestamps();
  },

  /**
   * 获取统计摘要
   * @returns {Promise<StatsSummary>} 包含总请求数、唯一路径、时间范围和热门路径的统计摘要
   */
  async getStatsSummary(): Promise<StatsSummary> {
    const timestamps = await this.getAllStatsTimestamps();
    const allTimestamps = [...timestamps.get, ...timestamps.post].sort((a, b) => a - b);

    let totalGetRequests = 0;
    let totalPostRequests = 0;
    const uniquePaths = new Set<string>();

    // 统计 GET 请求
    for (const timestamp of timestamps.get) {
      const stats = await this.getGetStats(timestamp);
      for (const [path, count] of stats) {
        totalGetRequests += count;
        uniquePaths.add(path);
      }
    }

    // 统计 POST 请求
    for (const timestamp of timestamps.post) {
      const stats = await this.getPostStats(timestamp);
      for (const [path, count] of stats) {
        totalPostRequests += count;
        uniquePaths.add(path);
      }
    }

    const timeRange = allTimestamps.length > 0
      ? { start: allTimestamps[0], end: allTimestamps[allTimestamps.length - 1] }
      : null;

    // 获取热门路径（简化版本，实际应该从所有数据中统计）
    const topPaths: Array<{ path: string; count: number; method: 'GET' | 'POST' }> = [];

    return {
      totalGetRequests,
      totalPostRequests,
      uniquePaths,
      timeRange,
      topPaths,
      generatedAt: Date.now()
    };
  },

  /**
   * 获取热门路径统计
   */
  async getTopPaths(limit: number = 10): Promise<{
    get: Array<{ path: string; count: number }>;
    post: Array<{ path: string; count: number }>;
  }> {
    const timestamps = await this.getAllStatsTimestamps();
    const getPathCounts = new Map<string, number>();
    const postPathCounts = new Map<string, number>();

    // 统计 GET 请求
    for (const timestamp of timestamps.get) {
      const stats = await this.getGetStats(timestamp);
      for (const [path, count] of stats) {
        getPathCounts.set(path, (getPathCounts.get(path) || 0) + count);
      }
    }

    // 统计 POST 请求
    for (const timestamp of timestamps.post) {
      const stats = await this.getPostStats(timestamp);
      for (const [path, count] of stats) {
        postPathCounts.set(path, (postPathCounts.get(path) || 0) + count);
      }
    }

    // 排序并取前 N 个
    const sortedGet = Array.from(getPathCounts.entries())
      .map(([path, count]) => ({ path, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, limit);

    const sortedPost = Array.from(postPathCounts.entries())
      .map(([path, count]) => ({ path, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, limit);

    return {
      get: sortedGet,
      post: sortedPost
    };
  },

  /**
   * 获取每日统计
   * @param {Date} date - 要查询的日期
   * @returns {Promise<DailyStats>} 包含指定日期的 GET 和 POST 请求统计数据
   */
  async getDailyStats(date: Date): Promise<DailyStats> {
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);
    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);

    const startTimestamp = Math.floor(startOfDay.getTime() / 1000);
    const endTimestamp = Math.floor(endOfDay.getTime() / 1000);

    const getStatsRange = await this.getGetStatsRange(startTimestamp, endTimestamp);
    const postStatsRange = await this.getPostStatsRange(startTimestamp, endTimestamp);

    const dailyGetStats = new Map<string, number>();
    const dailyPostStats = new Map<string, number>();

    // 合并 GET 统计
    for (const [, stats] of getStatsRange) {
      for (const [path, count] of stats) {
        dailyGetStats.set(path, (dailyGetStats.get(path) || 0) + count);
      }
    }

    // 合并 POST 统计
    for (const [, stats] of postStatsRange) {
      for (const [path, count] of stats) {
        dailyPostStats.set(path, (dailyPostStats.get(path) || 0) + count);
      }
    }

    // 计算总请求数和唯一路径数
    let totalRequests = 0;
    const uniquePaths = new Set<string>();

    for (const [path, count] of dailyGetStats) {
      totalRequests += count;
      uniquePaths.add(path);
    }

    for (const [path, count] of dailyPostStats) {
      totalRequests += count;
      uniquePaths.add(path);
    }

    return {
      get: dailyGetStats,
      post: dailyPostStats,
      date: date.toISOString().split('T')[0],
      totalRequests,
      uniquePathsCount: uniquePaths.size
    };
  },

  /**
   * 获取实时统计（当前小时）
   * @returns {Promise<CurrentHourStats>} 当前小时的 GET 和 POST 请求统计数据
   */
  async getCurrentHourStats(): Promise<CurrentHourStats> {
    const now = new Date();
    const currentHour = Math.floor(now.getTime() / 1000 / 3600) * 3600;

    const getStats = await this.getGetStats(currentHour);
    const postStats = await this.getPostStats(currentHour);

    return {
      get: getStats,
      post: postStats,
      hour: currentHour,
      lastUpdated: Date.now()
    };
  },

  /**
   * 导出统计数据
   */
  async exportStats(startDate: Date, endDate: Date): Promise<{
    metadata: {
      exportDate: string;
      startDate: string;
      endDate: string;
      totalRecords: number;
    };
    data: {
      timestamp: number;
      type: 'GET' | 'POST';
      stats: Record<string, number>;
    }[];
  }> {
    const startTimestamp = Math.floor(startDate.getTime() / 1000);
    const endTimestamp = Math.floor(endDate.getTime() / 1000);

    const getStatsRange = await this.getGetStatsRange(startTimestamp, endTimestamp);
    const postStatsRange = await this.getPostStatsRange(startTimestamp, endTimestamp);

    const data: {
      timestamp: number;
      type: 'GET' | 'POST';
      stats: Record<string, number>;
    }[] = [];

    // 添加 GET 统计
    for (const [timestamp, stats] of getStatsRange) {
      data.push({
        timestamp,
        type: 'GET',
        stats: Object.fromEntries(stats)
      });
    }

    // 添加 POST 统计
    for (const [timestamp, stats] of postStatsRange) {
      data.push({
        timestamp,
        type: 'POST',
        stats: Object.fromEntries(stats)
      });
    }

    // 按时间戳排序
    data.sort((a, b) => a.timestamp - b.timestamp);

    return {
      metadata: {
        exportDate: new Date().toISOString(),
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        totalRecords: data.length
      },
      data
    };
  }
};

export default statsManager;
