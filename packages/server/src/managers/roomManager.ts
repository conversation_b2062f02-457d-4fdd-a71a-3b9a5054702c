import type { RoomWithStatus } from '@/types/index.js';
import type { UserId, User, ServerSocket, ServerEmitEvents, ServerSocketData } from '@unibabble/shared';
import { RoomActiveStatus, getTimestamp } from '@unibabble/shared';
import { genRoomId } from '@/utils/index.js';
import { roomService } from '@/services/redis/index.js';
import { getCache } from '@/utils/hybridCache.js';
import type { RemoteSocket } from 'socket.io';

// 创建房间缓存实例
const roomCache = getCache<RoomWithStatus>('rooms', {
  localTTL: 2 * 60 * 1000, // 2分钟本地缓存
  redisTTL: 10 * 60, // 10分钟 Redis 缓存
  maxLocalSize: 500 // 最多缓存500个房间
});

/**
 * Redis-based Room Manager
 * 使用 Redis 存储房间状态数据
 */
export const roomManager = {
  /**
   * 创建房间
   */
  async createRoom(adminId: UserId, adminToken: string, roomId?: number): Promise<RoomWithStatus> {
    const timestamp = getTimestamp();
    const room: RoomWithStatus = {
      adminId,
      id: roomId ? roomId : genRoomId(),
      users: [],
      createdAt: timestamp,
      status: RoomActiveStatus.ACTIVE,
      lastActiveAt: timestamp,
      messageCounter: 0,
      kickedIds: new Set(),
      adminToken
    };

    await roomService.setRoom(room);
    return room;
  },

  /**
   * 获取房间信息
   */
  async getRoom(id: number): Promise<RoomWithStatus | null> {
    if (!id) return null;

    // 使用缓存获取房间信息
    return await roomCache.getOrSet(
      id.toString(),
      () => roomService.getRoom(id)
    );
  },

  /**
   * 更新房间信息
   */
  async updateRoom(
    room: RoomWithStatus,
    updates: Partial<RoomWithStatus>,
    updateTS = true
  ): Promise<RoomWithStatus> {
    if (updateTS) {
      updates.lastActiveAt = getTimestamp();
    }

    const updatedRoom = { ...room, ...updates };
    await roomService.setRoom(updatedRoom);

    // 更新缓存
    await roomCache.set(room.id.toString(), updatedRoom);

    return updatedRoom;
  },

  /**
   * 删除房间
   */
  async deleteRoom(roomId: number, socket?: ServerSocket): Promise<void> {
    await roomService.deleteRoom(roomId);

    // 删除缓存
    await roomCache.delete(roomId.toString());

    // 通知用户房间关闭
    if (socket) {
      await socket.leave(roomId.toString());
    }
  },

  /**
   * 清理非活跃房间
   */
  async cleanInactiveRooms(maxInactiveTime: number): Promise<number> {
    const timestamp = getTimestamp();
    const roomIds = await roomService.getAllRoomIds();
    let cleanedCount = 0;

    for (const roomId of roomIds) {
      const room = await roomService.getRoom(roomId);
      if (room && timestamp - room.lastActiveAt > maxInactiveTime) {
        await this.deleteRoom(roomId);
        cleanedCount++;
      }
    }

    return cleanedCount;
  },

  /**
   * 用户加入房间
   */
  async joinRoom(socket: ServerSocket, room: RoomWithStatus, user: User, userToken: string): Promise<void> {
    // 检查用户是否已在房间中
    if (!room.users.some(u => u.id === user.id)) {
      // 添加用户到房间
      await roomService.addUserToRoom(room.id, user);

      // 更新房间状态
      const updatedUsers = await roomService.getRoomUsers(room.id);
      await this.updateRoom(room, {
        users: updatedUsers,
        status: RoomActiveStatus.ACTIVE
      });
    }

    // 加入 Socket.IO 房间
    await socket.join(room.id.toString());
    socket.data.roomId = room.id;
    socket.data.userToken = userToken;
  },

  /**
   * 用户离开房间
   */
  async leaveRoom(
    socket: ServerSocket | RemoteSocket<ServerEmitEvents, ServerSocketData>,
    room: RoomWithStatus,
    userId: string
  ): Promise<RoomWithStatus> {
    // 从房间移除用户
    await roomService.removeUserFromRoom(room.id, userId);

    // 离开 Socket.IO 房间
    await socket.leave(room.id.toString());
    socket.data.roomId = undefined;

    // 获取更新后的用户列表
    const updatedUsers = await roomService.getRoomUsers(room.id);

    // 更新房间状态
    return await this.updateRoom(room, { users: updatedUsers });
  },

  /**
   * 踢出用户
   */
  async kickUser(roomId: number, userId: string): Promise<void> {
    await roomService.addKickedUser(roomId, userId);
    await roomService.removeUserFromRoom(roomId, userId);
  },

  /**
   * 检查用户是否被踢
   */
  async isUserKicked(roomId: number, userId: string): Promise<boolean> {
    return await roomService.isUserKicked(roomId, userId);
  },

  /**
   * 恢复被踢用户
   */
  async unkickUser(roomId: number, userId: string): Promise<void> {
    await roomService.removeKickedUser(roomId, userId);
  },

  /**
   * 更新房间最后活跃时间
   */
  async updateLastActiveTime(roomId: number, timestamp?: number): Promise<void> {
    const ts = timestamp || getTimestamp();
    await roomService.updateLastActiveTime(roomId, ts);
  },

  /**
   * 更新房间状态
   */
  async updateRoomStatus(roomId: number, status: RoomActiveStatus): Promise<void> {
    await roomService.updateRoomStatus(roomId, status);
  },

  /**
   * 增加消息计数器
   */
  async incrementMessageCounter(roomId: number): Promise<number> {
    return await roomService.incrementMessageCounter(roomId);
  },

  /**
   * 检查房间是否存在
   */
  async roomExists(roomId: number): Promise<boolean> {
    return await roomService.roomExists(roomId);
  },

  /**
   * 获取房间用户列表
   */
  async getRoomUsers(roomId: number): Promise<User[]> {
    return await roomService.getRoomUsers(roomId);
  },

  /**
   * 获取所有房间ID
   */
  async getAllRoomIds(): Promise<number[]> {
    return await roomService.getAllRoomIds();
  },

  /**
   * 获取房间统计信息
   */
  async getRoomStats(): Promise<{
    total: number;
    active: number;
    inactive: number;
    idle: number;
    totalMessages: number;
  }> {
    const roomIds = await this.getAllRoomIds();
    const stats = {
      total: roomIds.length,
      active: 0,
      inactive: 0,
      idle: 0,
      totalMessages: 0
    };

    for (const roomId of roomIds) {
      const room = await roomService.getRoom(roomId);
      if (room) {
        stats.totalMessages += room.messageCounter;

        switch (room.status) {
          case RoomActiveStatus.ACTIVE:
            stats.active++;
            break;
          case RoomActiveStatus.INACTIVE:
            stats.inactive++;
            break;
          case RoomActiveStatus.IDLE:
            stats.idle++;
            break;
        }
      }
    }

    return stats;
  },

  /**
   * 批量获取房间信息
   */
  async getRooms(roomIds: number[]): Promise<Map<number, RoomWithStatus>> => {
  const result = new Map<number, RoomWithStatus>();

  for (const roomId of roomIds) {
    const room = await roomService.getRoom(roomId);
    if (room) {
      result.set(roomId, room);
    }
  }

  return result;
},

  /**
   * 获取用户参与的所有房间
   */
  async getUserRooms(userId: string): Promise<RoomWithStatus[]> => {
  const roomIds = await this.getAllRoomIds();
  const userRooms: RoomWithStatus[] = [];

  for (const roomId of roomIds) {
    const room = await roomService.getRoom(roomId);
    if (room && room.users.some(user => user.id === userId)) {
      userRooms.push(room);
    }
  }

  return userRooms;
},

  /**
   * 清理空房间
   */
  async cleanEmptyRooms(): Promise < number > {
  const roomIds = await this.getAllRoomIds();
  let cleanedCount = 0;

  for(const roomId of roomIds) {
    const room = await roomService.getRoom(roomId);
    if (room && room.users.length === 0) {
      await this.deleteRoom(roomId);
      cleanedCount++;
    }
  }

    return cleanedCount;
}
};

export default roomManager;
