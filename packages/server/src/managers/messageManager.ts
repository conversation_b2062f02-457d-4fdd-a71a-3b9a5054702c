import type { UserRoomState } from '@/types/index.js';
import type { ServerSocket } from '@unibabble/shared';
import { getTimestamp, ErrorCode, logger } from '@unibabble/shared';
import { serConfig } from '@/configs/index.js';
import { userManager } from './userManager.js';
import { roomManager } from './roomManager.js';

const MODULE_NAME = 'Server:managers:message';

/**
 * Redis-based Message Manager
 * 使用 Redis 存储消息状态和速率限制数据
 */
export const messageManager = {
  /**
   * 检查消息速率限制
   */
  async checkMessageRateLimit(socket: ServerSocket): Promise<boolean> {
    const now = getTimestamp();
    const { userId, roomId, userToken, lang } = socket.data;
    
    if (!userId || !roomId || !userToken) {
      logger.warn('Missing socket data for rate limit check', {
        module: MODULE_NAME,
        method: 'checkMessageRateLimit',
        timestamp: now,
        details: { userId, roomId, userToken: !!userToken }
      });
      return false;
    }

    try {
      let state = await userManager.getUserRoomState(userId, roomId);
      if (!state) {
        state = { 
          userToken, 
          msgCount: 0, 
          timestamp: now, 
          lang: lang || 'en' 
        };
        await this.updateMessageRate(userId, roomId, state);
        return true;
      }

      const timeDiff = now - state.timestamp;
      if (timeDiff > serConfig.message.messageSendInterval) {
        await this.updateMessageRate(userId, roomId, { 
          ...state, 
          msgCount: state.msgCount + 1, 
          timestamp: now 
        });
        return true;
      } else {
        logger.debug('Message rate limit exceeded', {
          module: MODULE_NAME,
          method: 'checkMessageRateLimit',
          timestamp: now,
          details: {
            userId,
            roomId,
            timeDiff,
            required: serConfig.message.messageSendInterval,
            msgCount: state.msgCount
          }
        });
        return false;
      }
    } catch (error) {
      logger.error('Rate limit check failed', {
        module: MODULE_NAME,
        method: 'checkMessageRateLimit',
        timestamp: now,
        error: error instanceof Error ? error.message : 'Unknown error',
        details: { userId, roomId }
      });
      return false;
    }
  },

  /**
   * 更新消息速率状态
   */
  async updateMessageRate(userId: string, roomId: number, state: UserRoomState): Promise<void> {
    try {
      await userManager.addUserRoomState(userId, roomId, { 
        ...state, 
        timestamp: getTimestamp() 
      });
    } catch (error) {
      logger.error('Failed to update message rate', {
        module: MODULE_NAME,
        method: 'updateMessageRate',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error',
        details: { userId, roomId, state }
      });
    }
  },

  /**
   * 删除用户房间状态
   */
  async deleteUserRoomState(userId: string, roomId: number): Promise<void> {
    try {
      await userManager.deleteUserRoomState(userId, roomId);
    } catch (error) {
      logger.error('Failed to delete user room state', {
        module: MODULE_NAME,
        method: 'deleteUserRoomState',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error',
        details: { userId, roomId }
      });
    }
  },

  /**
   * 处理消息确认
   */
  async handleMessageAck(data: { roomId: number; messageId?: string; userId?: string }): Promise<void> {
    try {
      const room = await roomManager.getRoom(data.roomId);
      if (room) {
        await roomManager.incrementMessageCounter(data.roomId);
        await roomManager.updateLastActiveTime(data.roomId);
        
        logger.debug('Message acknowledged', {
          module: MODULE_NAME,
          method: 'handleMessageAck',
          timestamp: getTimestamp(),
          details: {
            roomId: data.roomId,
            messageId: data.messageId,
            userId: data.userId,
            newCounter: room.messageCounter + 1
          }
        });
      }
    } catch (error) {
      logger.error('Failed to handle message ack', {
        module: MODULE_NAME,
        method: 'handleMessageAck',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error',
        details: data
      });
    }
  },

  /**
   * 获取用户消息统计
   */
  async getUserMessageStats(userId: string): Promise<{
    totalRooms: number;
    totalMessages: number;
    roomStats: Array<{
      roomId: number;
      messageCount: number;
      lastActivity: number;
    }>;
  }> {
    try {
      const roomStates = await userManager.getAllUserRoomStates(userId);
      const roomStats: Array<{
        roomId: number;
        messageCount: number;
        lastActivity: number;
      }> = [];

      let totalMessages = 0;

      for (const [roomId, state] of roomStates) {
        roomStats.push({
          roomId,
          messageCount: state.msgCount,
          lastActivity: state.timestamp
        });
        totalMessages += state.msgCount;
      }

      return {
        totalRooms: roomStates.size,
        totalMessages,
        roomStats: roomStats.sort((a, b) => b.lastActivity - a.lastActivity)
      };
    } catch (error) {
      logger.error('Failed to get user message stats', {
        module: MODULE_NAME,
        method: 'getUserMessageStats',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error',
        details: { userId }
      });
      return {
        totalRooms: 0,
        totalMessages: 0,
        roomStats: []
      };
    }
  },

  /**
   * 获取房间消息统计
   */
  async getRoomMessageStats(roomId: number): Promise<{
    totalMessages: number;
    totalUsers: number;
    userStats: Array<{
      userId: string;
      messageCount: number;
      lastActivity: number;
    }>;
  }> {
    try {
      const room = await roomManager.getRoom(roomId);
      if (!room) {
        return {
          totalMessages: 0,
          totalUsers: 0,
          userStats: []
        };
      }

      const userStats: Array<{
        userId: string;
        messageCount: number;
        lastActivity: number;
      }> = [];

      for (const user of room.users) {
        const state = await userManager.getUserRoomState(user.id, roomId);
        if (state) {
          userStats.push({
            userId: user.id,
            messageCount: state.msgCount,
            lastActivity: state.timestamp
          });
        }
      }

      return {
        totalMessages: room.messageCounter,
        totalUsers: room.users.length,
        userStats: userStats.sort((a, b) => b.messageCount - a.messageCount)
      };
    } catch (error) {
      logger.error('Failed to get room message stats', {
        module: MODULE_NAME,
        method: 'getRoomMessageStats',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error',
        details: { roomId }
      });
      return {
        totalMessages: 0,
        totalUsers: 0,
        userStats: []
      };
    }
  },

  /**
   * 重置用户消息计数
   */
  async resetUserMessageCount(userId: string, roomId: number): Promise<void> {
    try {
      const state = await userManager.getUserRoomState(userId, roomId);
      if (state) {
        await this.updateMessageRate(userId, roomId, {
          ...state,
          msgCount: 0
        });
        
        logger.info('User message count reset', {
          module: MODULE_NAME,
          method: 'resetUserMessageCount',
          timestamp: getTimestamp(),
          details: { userId, roomId }
        });
      }
    } catch (error) {
      logger.error('Failed to reset user message count', {
        module: MODULE_NAME,
        method: 'resetUserMessageCount',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error',
        details: { userId, roomId }
      });
    }
  },

  /**
   * 批量清理过期的消息状态
   */
  async cleanupExpiredMessageStates(): Promise<number> {
    try {
      const userIds = await userManager.getAllUserIds();
      let totalCleaned = 0;

      for (const userId of userIds) {
        const cleaned = await userManager.cleanupExpiredUserRoomStates(userId);
        totalCleaned += cleaned;
      }

      logger.info('Message states cleanup completed', {
        module: MODULE_NAME,
        method: 'cleanupExpiredMessageStates',
        timestamp: getTimestamp(),
        details: { totalCleaned, totalUsers: userIds.length }
      });

      return totalCleaned;
    } catch (error) {
      logger.error('Failed to cleanup expired message states', {
        module: MODULE_NAME,
        method: 'cleanupExpiredMessageStates',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return 0;
    }
  },

  /**
   * 检查用户是否可以发送消息
   */
  async canUserSendMessage(userId: string, roomId: number): Promise<{
    canSend: boolean;
    reason?: string;
    waitTime?: number;
  }> {
    try {
      // 检查用户是否存在
      const user = await userManager.getUser(userId);
      if (!user) {
        return {
          canSend: false,
          reason: 'User not found'
        };
      }

      // 检查房间是否存在
      const room = await roomManager.getRoom(roomId);
      if (!room) {
        return {
          canSend: false,
          reason: 'Room not found'
        };
      }

      // 检查用户是否被踢
      const isKicked = await roomManager.isUserKicked(roomId, userId);
      if (isKicked) {
        return {
          canSend: false,
          reason: 'User is banned from this room'
        };
      }

      // 检查速率限制
      const state = await userManager.getUserRoomState(userId, roomId);
      if (state) {
        const now = getTimestamp();
        const timeDiff = now - state.timestamp;
        const requiredInterval = serConfig.message.messageSendInterval;
        
        if (timeDiff < requiredInterval) {
          return {
            canSend: false,
            reason: 'Rate limit exceeded',
            waitTime: requiredInterval - timeDiff
          };
        }
      }

      return { canSend: true };
    } catch (error) {
      logger.error('Failed to check if user can send message', {
        module: MODULE_NAME,
        method: 'canUserSendMessage',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error',
        details: { userId, roomId }
      });
      return {
        canSend: false,
        reason: 'Internal error'
      };
    }
  }
};

export default messageManager;
