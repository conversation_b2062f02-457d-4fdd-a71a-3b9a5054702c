import type { UserRoomState, UserWithStatus } from '@/types/index.js';
import type { User } from '@unibabble/shared';
import { userService } from '@/services/redis/index.js';
import { getCache } from '@/utils/hybridCache.js';

// 创建用户缓存实例
const userCache = getCache<UserWithStatus>('users', {
  localTTL: 3 * 60 * 1000, // 3分钟本地缓存
  redisTTL: 15 * 60, // 15分钟 Redis 缓存
  maxLocalSize: 1000 // 最多缓存1000个用户
});

// 创建用户房间状态缓存实例
const userRoomStateCache = getCache<UserRoomState>('user-room-states', {
  localTTL: 1 * 60 * 1000, // 1分钟本地缓存
  redisTTL: 5 * 60, // 5分钟 Redis 缓存
  maxLocalSize: 2000 // 最多缓存2000个状态
});

/**
 * Redis-based User Manager
 * 使用 Redis 存储用户状态数据
 */
export const userManager = {
  /**
   * 添加用户
   */
  async addUser(user: UserWithStatus): Promise<void> {
    await userService.setUser(user);
    // 更新缓存
    await userCache.set(user.id, user);
  },

  /**
   * 获取用户信息
   */
  async getUser(userId: string): Promise<User | null> {
    const userWithStatus = await this.getUserWithStatus(userId);
    return userWithStatus;
  },

  /**
   * 获取带状态的用户信息
   */
  async getUserWithStatus(userId: string): Promise<UserWithStatus | null> {
    return await userCache.getOrSet(
      userId,
      () => userService.getUserWithStatus(userId)
    );
  },

  /**
   * 删除用户
   */
  async deleteUser(userId: string): Promise<boolean> {
    const result = await userService.deleteUser(userId);
    // 删除缓存
    await userCache.delete(userId);
    return result;
  },

  /**
   * 检查用户是否存在
   */
  async userExists(userId: string): Promise<boolean> {
    return await userService.userExists(userId);
  },

  /**
   * 添加用户房间状态
   */
  async addUserRoomState(userId: string, roomId: number, state: UserRoomState): Promise<void> {
    await userService.setUserRoomState(userId, roomId, state);
    // 更新缓存
    const cacheKey = `${userId}:${roomId}`;
    await userRoomStateCache.set(cacheKey, state);
  },

  /**
   * 获取用户房间状态
   */
  async getUserRoomState(userId: string, roomId: number): Promise<UserRoomState | null> {
    const cacheKey = `${userId}:${roomId}`;
    return await userRoomStateCache.getOrSet(
      cacheKey,
      () => userService.getUserRoomState(userId, roomId)
    );
  },

  /**
   * 获取用户所有房间状态
   */
  async getAllUserRoomStates(userId: string): Promise<Map<number, UserRoomState>> {
    return await userService.getAllUserRoomStates(userId);
  },

  /**
   * 删除用户房间状态
   */
  async deleteUserRoomState(userId: string, roomId: number): Promise<void> {
    await userService.deleteUserRoomState(userId, roomId);
  },

  /**
   * 获取用户参与的所有房间ID
   */
  async getUserRoomIds(userId: string): Promise<number[]> {
    return await userService.getUserRoomIds(userId);
  },

  /**
   * 更新用户消息计数
   */
  async updateUserMessageCount(userId: string, roomId: number, increment: number = 1): Promise<number> {
    return await userService.updateUserMessageCount(userId, roomId, increment);
  },

  /**
   * 更新用户房间状态时间戳
   */
  async updateUserRoomTimestamp(userId: string, roomId: number, timestamp?: number): Promise<void> {
    await userService.updateUserRoomTimestamp(userId, roomId, timestamp);
  },

  /**
   * 批量获取用户信息
   */
  async getUsers(userIds: string[]): Promise<Map<string, User>> {
    return await userService.getUsers(userIds);
  },

  /**
   * 批量设置用户信息
   */
  async setUsers(users: UserWithStatus[]): Promise<void> {
    await userService.setUsers(users);
  },

  /**
   * 获取所有用户ID
   */
  async getAllUserIds(): Promise<string[]> {
    return await userService.getAllUserIds();
  },

  /**
   * 清理过期的用户房间状态
   */
  async cleanupExpiredUserRoomStates(userId: string): Promise<number> {
    return await userService.cleanupExpiredUserRoomStates(userId);
  },

  /**
   * 检查管理员令牌
   * 验证用户是否是房间管理员
   */
  async checkAdminToken(kickerId: string, roomId: number, userToken: string): Promise<boolean> {
    const kickerRoomState = await this.getUserRoomState(kickerId, roomId);
    if (!kickerRoomState) return false;

    // 检查用户令牌是否匹配
    return kickerRoomState.userToken === userToken;
  },

  /**
   * 获取用户统计信息
   */
  async getUserStats(): Promise<{
    total: number;
    activeUsers: number;
    totalRoomStates: number;
  }> {
    const userIds = await this.getAllUserIds();
    let activeUsers = 0;
    let totalRoomStates = 0;

    for (const userId of userIds) {
      const user = await this.getUser(userId);
      if (user) {
        activeUsers++;
      }

      const roomStates = await this.getAllUserRoomStates(userId);
      totalRoomStates += roomStates.size;
    }

    return {
      total: userIds.length,
      activeUsers,
      totalRoomStates
    };
  },

  /**
   * 清理非活跃用户
   */
  async cleanupInactiveUsers(maxInactiveTime: number): Promise<number> {
    const userIds = await this.getAllUserIds();
    const now = Date.now();
    let cleanedCount = 0;

    for (const userId of userIds) {
      const roomStates = await this.getAllUserRoomStates(userId);
      let hasActiveState = false;

      for (const [, state] of roomStates) {
        if (now - state.timestamp <= maxInactiveTime) {
          hasActiveState = true;
          break;
        }
      }

      // 如果用户没有活跃状态，删除用户
      if (!hasActiveState && roomStates.size > 0) {
        await this.deleteUser(userId);
        cleanedCount++;
      }
    }

    return cleanedCount;
  },

  /**
   * 获取房间中的所有用户
   */
  async getUsersInRoom(roomId: number): Promise<User[]> {
    const userIds = await this.getAllUserIds();
    const usersInRoom: User[] = [];

    for (const userId of userIds) {
      const roomState = await this.getUserRoomState(userId, roomId);
      if (roomState) {
        const user = await this.getUser(userId);
        if (user) {
          usersInRoom.push(user);
        }
      }
    }

    return usersInRoom;
  },

  /**
   * 更新用户语言设置
   */
  async updateUserLanguage(userId: string, lang: any): Promise<void> {
    const user = await this.getUserWithStatus(userId);
    if (user) {
      user.lang = lang;
      await this.addUser(user);
    }
  },

  /**
   * 检查用户是否在房间中
   */
  async isUserInRoom(userId: string, roomId: number): Promise<boolean> {
    const roomState = await this.getUserRoomState(userId, roomId);
    return roomState !== null;
  },

  /**
   * 获取用户最近活跃的房间
   */
  async getUserMostRecentRoom(userId: string): Promise<number | null> {
    const roomStates = await this.getAllUserRoomStates(userId);
    let mostRecentRoomId: number | null = null;
    let mostRecentTimestamp = 0;

    for (const [roomId, state] of roomStates) {
      if (state.timestamp > mostRecentTimestamp) {
        mostRecentTimestamp = state.timestamp;
        mostRecentRoomId = roomId;
      }
    }

    return mostRecentRoomId;
  },

  /**
   * 批量清理用户房间状态
   */
  async batchCleanupUserRoomStates(userIds: string[]): Promise<number> {
    let totalCleaned = 0;

    for (const userId of userIds) {
      const cleaned = await this.cleanupExpiredUserRoomStates(userId);
      totalCleaned += cleaned;
    }

    return totalCleaned;
  }
};

export default userManager;
