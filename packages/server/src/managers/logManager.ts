import { logService } from '@/services/redis/logging/logService.js';
import { loggingConfig, LogLevel, isLogLevelEnabled } from '@/configs/logging.js';
import type { 
  LogEntry, 
  LogQueryParams, 
  LogQueryResult, 
  LogStatsParams, 
  LogStatsResult,
  LogCleanupResult,
  BatchLogEntry,
  LogBatchOperationResult
} from '@/types/logging.js';
import { logger, getTimestamp } from '@unibabble/shared';
import { v4 as uuidv4 } from 'uuid';
import { EventEmitter } from 'events';

const MODULE_NAME = 'Server:managers:logManager';

/**
 * 日志管理器
 * 提供高级日志管理功能，包括批量处理、缓冲、事件发布等
 */
class LogManager extends EventEmitter {
  private batchBuffer: Map<LogLevel, LogEntry[]> = new Map();
  private batchTimer: NodeJS.Timeout | null = null;
  private isProcessing = false;
  private writeQueue: LogEntry[] = [];
  private concurrentWrites = 0;

  constructor() {
    super();
    this.initializeBatchBuffer();
    this.startBatchTimer();
  }

  /**
   * 初始化批量缓冲区
   */
  private initializeBatchBuffer(): void {
    Object.values(LogLevel).forEach(level => {
      this.batchBuffer.set(level, []);
    });
  }

  /**
   * 启动批量处理定时器
   */
  private startBatchTimer(): void {
    if (this.batchTimer) {
      clearInterval(this.batchTimer);
    }

    this.batchTimer = setInterval(() => {
      this.flushBatch();
    }, loggingConfig.batch.flushInterval);
  }

  /**
   * 写入单个日志条目
   */
  async writeLog(entry: Omit<LogEntry, 'id' | 'timestamp'>): Promise<void> {
    if (!loggingConfig.enabled || !isLogLevelEnabled(entry.level)) {
      return;
    }

    const logEntry: LogEntry = {
      ...entry,
      id: uuidv4(),
      timestamp: Date.now()
    };

    try {
      // 添加到批量缓冲区
      const levelBuffer = this.batchBuffer.get(entry.level);
      if (levelBuffer) {
        levelBuffer.push(logEntry);

        // 检查是否需要立即刷新
        if (levelBuffer.length >= loggingConfig.batch.size) {
          await this.flushLevelBatch(entry.level);
        }
      }

      // 发布实时事件
      this.emit('log_entry', logEntry);

    } catch (error) {
      logger.error('Failed to write log entry', {
        module: MODULE_NAME,
        method: 'writeLog',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error',
        details: { level: entry.level, message: entry.message }
      });
    }
  }

  /**
   * 批量写入日志条目
   */
  async writeLogs(entries: Omit<LogEntry, 'id' | 'timestamp'>[]): Promise<LogBatchOperationResult> {
    const result: LogBatchOperationResult = {
      success: true,
      processedCount: 0,
      errorCount: 0,
      errors: [],
      duration: 0
    };

    const startTime = Date.now();

    try {
      const logEntries: LogEntry[] = entries.map(entry => ({
        ...entry,
        id: uuidv4(),
        timestamp: Date.now()
      }));

      // 按级别分组
      const entriesByLevel = new Map<LogLevel, LogEntry[]>();
      
      for (const entry of logEntries) {
        if (!isLogLevelEnabled(entry.level)) {
          continue;
        }

        if (!entriesByLevel.has(entry.level)) {
          entriesByLevel.set(entry.level, []);
        }
        entriesByLevel.get(entry.level)!.push(entry);
      }

      // 批量写入每个级别
      for (const [level, levelEntries] of entriesByLevel) {
        try {
          await logService.writeLogEntries(levelEntries);
          result.processedCount += levelEntries.length;

          // 发布批量完成事件
          const batchEntry: BatchLogEntry = {
            entries: levelEntries,
            batchId: uuidv4(),
            timestamp: Date.now(),
            totalSize: levelEntries.length
          };
          this.emit('batch_complete', batchEntry);

        } catch (error) {
          result.errorCount += levelEntries.length;
          result.errors.push({
            entryId: `batch_${level}`,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      result.success = result.errorCount === 0;
      result.duration = Date.now() - startTime;

      return result;

    } catch (error) {
      result.success = false;
      result.errorCount = entries.length;
      result.duration = Date.now() - startTime;
      result.errors.push({
        entryId: 'batch_operation',
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      logger.error('Failed to write log batch', {
        module: MODULE_NAME,
        method: 'writeLogs',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error',
        details: { entriesCount: entries.length }
      });

      return result;
    }
  }

  /**
   * 刷新指定级别的批量缓冲区
   */
  private async flushLevelBatch(level: LogLevel): Promise<void> {
    const levelBuffer = this.batchBuffer.get(level);
    if (!levelBuffer || levelBuffer.length === 0) {
      return;
    }

    const entries = [...levelBuffer];
    levelBuffer.length = 0; // 清空缓冲区

    try {
      await logService.writeLogEntries(entries);

      // 发布批量完成事件
      const batchEntry: BatchLogEntry = {
        entries,
        batchId: uuidv4(),
        timestamp: Date.now(),
        totalSize: entries.length
      };
      this.emit('batch_complete', batchEntry);

    } catch (error) {
      logger.error('Failed to flush level batch', {
        module: MODULE_NAME,
        method: 'flushLevelBatch',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error',
        details: { level, entriesCount: entries.length }
      });

      // 重新添加到缓冲区进行重试
      levelBuffer.unshift(...entries);
    }
  }

  /**
   * 刷新所有批量缓冲区
   */
  private async flushBatch(): Promise<void> {
    if (this.isProcessing) {
      return;
    }

    this.isProcessing = true;

    try {
      const flushPromises: Promise<void>[] = [];

      for (const level of Object.values(LogLevel)) {
        const levelBuffer = this.batchBuffer.get(level);
        if (levelBuffer && levelBuffer.length > 0) {
          flushPromises.push(this.flushLevelBatch(level));
        }
      }

      await Promise.all(flushPromises);

    } catch (error) {
      logger.error('Failed to flush batch', {
        module: MODULE_NAME,
        method: 'flushBatch',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * 查询日志条目
   */
  async queryLogs(params: LogQueryParams): Promise<LogQueryResult> {
    try {
      return await logService.queryLogEntries(params);
    } catch (error) {
      logger.error('Failed to query logs', {
        module: MODULE_NAME,
        method: 'queryLogs',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error',
        details: params
      });
      throw error;
    }
  }

  /**
   * 获取日志统计
   */
  async getStats(params: LogStatsParams = {}): Promise<LogStatsResult> {
    try {
      return await logService.getLogStats(params);
    } catch (error) {
      logger.error('Failed to get log stats', {
        module: MODULE_NAME,
        method: 'getStats',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error',
        details: params
      });
      throw error;
    }
  }

  /**
   * 清理过期日志
   */
  async cleanup(): Promise<LogCleanupResult> {
    try {
      const result = await logService.cleanupExpiredLogs();
      
      // 发布清理完成事件
      this.emit('cleanup_complete', result);
      
      return result;
    } catch (error) {
      logger.error('Failed to cleanup logs', {
        module: MODULE_NAME,
        method: 'cleanup',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * 获取日志健康状态
   */
  async getHealth() {
    try {
      return await logService.getLogHealth();
    } catch (error) {
      logger.error('Failed to get log health', {
        module: MODULE_NAME,
        method: 'getHealth',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * 强制刷新所有缓冲区
   */
  async forceFlush(): Promise<void> {
    await this.flushBatch();
  }

  /**
   * 销毁日志管理器
   */
  destroy(): void {
    if (this.batchTimer) {
      clearInterval(this.batchTimer);
      this.batchTimer = null;
    }

    // 最后一次刷新
    this.flushBatch().catch(error => {
      logger.error('Failed to flush on destroy', {
        module: MODULE_NAME,
        method: 'destroy',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    });

    this.removeAllListeners();
  }

  /**
   * 便捷方法：记录调试日志
   */
  async debug(message: string, metadata?: Record<string, any>): Promise<void> {
    await this.writeLog({
      level: LogLevel.DEBUG,
      message,
      metadata
    });
  }

  /**
   * 便捷方法：记录信息日志
   */
  async info(message: string, metadata?: Record<string, any>): Promise<void> {
    await this.writeLog({
      level: LogLevel.INFO,
      message,
      metadata
    });
  }

  /**
   * 便捷方法：记录警告日志
   */
  async warn(message: string, metadata?: Record<string, any>): Promise<void> {
    await this.writeLog({
      level: LogLevel.WARN,
      message,
      metadata
    });
  }

  /**
   * 便捷方法：记录错误日志
   */
  async error(message: string, error?: Error, metadata?: Record<string, any>): Promise<void> {
    await this.writeLog({
      level: LogLevel.ERROR,
      message,
      error: error ? {
        name: error.name,
        message: error.message,
        stack: error.stack
      } : undefined,
      metadata
    });
  }
}

// 全局日志管理器实例
export const logManager = new LogManager();

// 进程退出时清理
// kill
process.on('SIGTERM', () => {
  logManager.destroy();
});

// ctrl + c
process.on('SIGINT', () => {
  logManager.destroy();
});

export default LogManager;
