import { Redis } from 'ioredis';
import { createRedisClient } from '@/configs/redis.js';
import { logger, getTimestamp } from '@unibabble/shared';
import type {
  RedisConnectionStatus,
  RedisConnectionMetrics,
  REDIS_HEALTH_CONSTANTS
} from '@/types/redis-health.js';

const MODULE_NAME = 'Server:utils:redis:client';

// Redis 客户端管理器
class RedisClientManager {
  private redisInstance: Redis | null = null;
  private connectionStatus: RedisConnectionStatus = RedisConnectionStatus.DISCONNECTED;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 10;
  private reconnectDelay = 1000; // 1秒
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private lastError: Error | null = null;
  private connectionMetrics: Omit<RedisConnectionMetrics, 'status' | 'lastError' | 'reconnectAttempts'> = {
    totalConnections: 0,
    totalDisconnections: 0,
    totalErrors: 0,
    lastConnectedAt: 0,
    lastDisconnectedAt: 0,
    uptime: 0
  };

  constructor() {
    this.startHealthCheck();
  }

  /**
   * 获取 Redis 客户端实例
   */
  getClient(): Redis {
    if (!this.redisInstance) {
      this.redisInstance = this.createClient();
    }
    return this.redisInstance;
  }

  /**
   * 创建 Redis 客户端
   */
  private createClient(): Redis {
    const client = createRedisClient();
    this.setupEventListeners(client);
    this.connectionStatus = RedisConnectionStatus.CONNECTING;
    return client;
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(redis: Redis): void {
    redis.on('connect', () => {
      this.connectionStatus = RedisConnectionStatus.CONNECTED;
      this.connectionMetrics.totalConnections++;
      this.connectionMetrics.lastConnectedAt = Date.now();
      this.reconnectAttempts = 0;
      this.lastError = null;

      logger.info('Redis client connected', {
        module: MODULE_NAME,
        method: 'connect',
        timestamp: getTimestamp(),
        details: {
          attempts: this.reconnectAttempts,
          metrics: this.connectionMetrics
        }
      });
    });

    redis.on('ready', () => {
      this.connectionStatus = RedisConnectionStatus.CONNECTED;

      logger.info('Redis client ready', {
        module: MODULE_NAME,
        method: 'ready',
        timestamp: getTimestamp()
      });
    });

    redis.on('error', (error: Error) => {
      this.connectionStatus = RedisConnectionStatus.ERROR;
      this.connectionMetrics.totalErrors++;
      this.lastError = error;

      logger.error('Redis client error', {
        module: MODULE_NAME,
        method: 'error',
        timestamp: getTimestamp(),
        error: error.message,
        details: {
          errorCode: (error as any).code,
          errorErrno: (error as any).errno,
          metrics: this.connectionMetrics
        }
      });
    });

    redis.on('close', () => {
      this.connectionStatus = RedisConnectionStatus.DISCONNECTED;
      this.connectionMetrics.totalDisconnections++;
      this.connectionMetrics.lastDisconnectedAt = Date.now();

      logger.warn('Redis connection closed', {
        module: MODULE_NAME,
        method: 'close',
        timestamp: getTimestamp(),
        details: { metrics: this.connectionMetrics }
      });
    });

    redis.on('reconnecting', (delay: number) => {
      this.connectionStatus = RedisConnectionStatus.RECONNECTING;
      this.reconnectAttempts++;

      logger.info('Redis client reconnecting', {
        module: MODULE_NAME,
        method: 'reconnecting',
        timestamp: getTimestamp(),
        details: {
          attempt: this.reconnectAttempts,
          delay,
          maxAttempts: this.maxReconnectAttempts
        }
      });

      // 如果重连次数过多，考虑重置连接
      if (this.reconnectAttempts >= this.maxReconnectAttempts) {
        logger.error('Max reconnection attempts reached', {
          module: MODULE_NAME,
          method: 'reconnecting',
          timestamp: getTimestamp(),
          details: {
            attempts: this.reconnectAttempts,
            maxAttempts: this.maxReconnectAttempts
          }
        });

        // 重置连接
        setTimeout(() => {
          this.resetConnection();
        }, 5000);
      }
    });

    redis.on('end', () => {
      this.connectionStatus = RedisConnectionStatus.DISCONNECTED;

      logger.warn('Redis connection ended', {
        module: MODULE_NAME,
        method: 'end',
        timestamp: getTimestamp()
      });
    });
  }

  /**
   * 重置连接
   */
  private async resetConnection(): Promise<void> {
    try {
      logger.info('Resetting Redis connection', {
        module: MODULE_NAME,
        method: 'resetConnection',
        timestamp: getTimestamp()
      });

      if (this.redisInstance) {
        this.redisInstance.disconnect();
        this.redisInstance = null;
      }

      this.reconnectAttempts = 0;
      this.connectionStatus = RedisConnectionStatus.DISCONNECTED;

      // 重新创建连接
      setTimeout(() => {
        this.redisInstance = this.createClient();
      }, this.reconnectDelay);

    } catch (error) {
      logger.error('Failed to reset Redis connection', {
        module: MODULE_NAME,
        method: 'resetConnection',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * 启动健康检查
   */
  private startHealthCheck(): void {
    this.healthCheckInterval = setInterval(async () => {
      try {
        const isHealthy = await this.isHealthy();

        if (this.connectionStatus === RedisConnectionStatus.CONNECTED) {
          this.connectionMetrics.uptime = Date.now() - this.connectionMetrics.lastConnectedAt;
        }

        if (!isHealthy && this.connectionStatus === RedisConnectionStatus.CONNECTED) {
          logger.warn('Redis health check failed while status is connected', {
            module: MODULE_NAME,
            method: 'healthCheck',
            timestamp: getTimestamp(),
            details: { status: this.connectionStatus }
          });
        }
      } catch (error) {
        logger.error('Health check error', {
          module: MODULE_NAME,
          method: 'healthCheck',
          timestamp: getTimestamp(),
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }, 30000); // 每30秒检查一次
  }

  /**
   * 检查连接健康状态
   */
  async isHealthy(): Promise<boolean> {
    try {
      if (!this.redisInstance || this.connectionStatus !== RedisConnectionStatus.CONNECTED) {
        return false;
      }

      const result = await this.redisInstance.ping();
      return result === 'PONG';
    } catch (error) {
      logger.debug('Redis ping failed', {
        module: MODULE_NAME,
        method: 'isHealthy',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return false;
    }
  }

  /**
   * 获取连接状态
   */
  getConnectionStatus(): RedisConnectionStatus {
    return this.connectionStatus;
  }

  /**
   * 获取连接指标
   */
  getConnectionMetrics(): RedisConnectionMetrics {
    return {
      ...this.connectionMetrics,
      status: this.connectionStatus,
      lastError: this.lastError?.message,
      reconnectAttempts: this.reconnectAttempts
    };
  }

  /**
   * 安全执行 Redis 操作
   */
  async safeExecute<T>(operation: (redis: Redis) => Promise<T>, fallback?: T): Promise<T | null> {
    try {
      if (!await this.isHealthy()) {
        throw new Error('Redis connection is not healthy');
      }

      return await operation(this.getClient());
    } catch (error) {
      logger.error('Redis operation failed', {
        module: MODULE_NAME,
        method: 'safeExecute',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      return fallback !== undefined ? fallback : null;
    }
  }

  /**
   * 销毁连接
   */
  destroy(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }

    if (this.redisInstance) {
      this.redisInstance.disconnect();
      this.redisInstance = null;
    }

    this.connectionStatus = RedisConnectionStatus.DISCONNECTED;
  }
}

// 全局 Redis 客户端管理器实例
const redisClientManager = new RedisClientManager();

// 导出函数
export const getRedisClient = (): Redis => {
  return redisClientManager.getClient();
};

export const isRedisHealthy = async (): Promise<boolean> => {
  return await redisClientManager.isHealthy();
};

export const getRedisConnectionStatus = (): RedisConnectionStatus => {
  return redisClientManager.getConnectionStatus();
};

export const getRedisConnectionMetrics = (): RedisConnectionMetrics => {
  return redisClientManager.getConnectionMetrics();
};

export const safeRedisExecute = async <T>(
  operation: (redis: Redis) => Promise<T>,
  fallback?: T
): Promise<T | null> => {
  return await redisClientManager.safeExecute(operation, fallback);
};

// 默认导出 Redis 客户端实例
export const redis = getRedisClient();
export default redis;

// 进程退出时清理连接
process.on('SIGTERM', () => {
  redisClientManager.destroy();
});

process.on('SIGINT', () => {
  redisClientManager.destroy();
});
