import { logger, getTimestamp } from '@unibabble/shared';
import { redis } from '@/utils/redis/client.js';
import type { CacheMetrics } from '@/types/health.js';

const MODULE_NAME = 'Server:utils:hybridCache';

/**
 * 缓存项接口
 */
interface CacheItem<T> {
  data: T;
  timestamp: number;
  ttl: number;
  accessCount: number;
  lastAccess: number;
}

/**
 * 缓存配置接口
 */
interface CacheConfig {
  localTTL: number; // 本地缓存 TTL (毫秒)
  redisTTL: number; // Redis 缓存 TTL (秒)
  maxLocalSize: number; // 本地缓存最大条目数
  syncInterval: number; // 同步间隔 (毫秒)
  enableCompression: boolean; // 是否启用压缩
  enableMetrics: boolean; // 是否启用指标收集
}

/**
 * 缓存指标
 */
interface CacheMetrics {
  localHits: number;
  localMisses: number;
  redisHits: number;
  redisMisses: number;
  evictions: number;
  errors: number;
  totalRequests: number;
}

/**
 * 混合缓存系统
 * 结合本地内存缓存和 Redis 缓存，提供高性能的数据访问
 */
export class HybridCache<T> {
  private localCache = new Map<string, CacheItem<T>>();
  private config: CacheConfig;
  private metrics: CacheMetrics;
  private syncTimer: NodeJS.Timeout | null = null;
  private namespace: string;

  constructor(namespace: string, config: Partial<CacheConfig> = {}) {
    this.namespace = namespace;
    this.config = {
      localTTL: 5 * 60 * 1000, // 5分钟
      redisTTL: 30 * 60, // 30分钟
      maxLocalSize: 1000,
      syncInterval: 60 * 1000, // 1分钟
      enableCompression: false,
      enableMetrics: true,
      ...config
    };

    this.metrics = {
      localHits: 0,
      localMisses: 0,
      redisHits: 0,
      redisMisses: 0,
      evictions: 0,
      errors: 0,
      totalRequests: 0
    };

    this.startSyncTimer();
  }

  /**
   * 获取缓存数据
   */
  async get(key: string): Promise<T | null> {
    this.metrics.totalRequests++;
    const fullKey = this.getFullKey(key);

    try {
      // 1. 先检查本地缓存
      const localItem = this.localCache.get(key);
      if (localItem && this.isLocalItemValid(localItem)) {
        localItem.accessCount++;
        localItem.lastAccess = Date.now();
        this.metrics.localHits++;

        logger.debug('Local cache hit', {
          module: MODULE_NAME,
          method: 'get',
          timestamp: getTimestamp(),
          details: { namespace: this.namespace, key }
        });

        return localItem.data;
      }

      this.metrics.localMisses++;

      // 2. 检查 Redis 缓存
      const redisValue = await redis.get(fullKey);
      if (redisValue) {
        this.metrics.redisHits++;

        let data: T;
        try {
          data = JSON.parse(redisValue);
        } catch (error) {
          data = redisValue as unknown as T;
        }

        // 将数据存储到本地缓存
        this.setLocal(key, data);

        logger.debug('Redis cache hit', {
          module: MODULE_NAME,
          method: 'get',
          timestamp: getTimestamp(),
          details: { namespace: this.namespace, key }
        });

        return data;
      }

      this.metrics.redisMisses++;
      return null;
    } catch (error) {
      this.metrics.errors++;
      logger.error('Cache get error', {
        module: MODULE_NAME,
        method: 'get',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error',
        details: { namespace: this.namespace, key }
      });
      return null;
    }
  }

  /**
   * 设置缓存数据
   */
  async set(key: string, data: T, customTTL?: number): Promise<void> {
    const fullKey = this.getFullKey(key);

    try {
      // 1. 设置本地缓存
      this.setLocal(key, data, customTTL);

      // 2. 设置 Redis 缓存
      const serializedData = JSON.stringify(data);
      const ttl = customTTL ? Math.floor(customTTL / 1000) : this.config.redisTTL;

      await redis.setex(fullKey, ttl, serializedData);

      logger.debug('Cache set', {
        module: MODULE_NAME,
        method: 'set',
        timestamp: getTimestamp(),
        details: { namespace: this.namespace, key, ttl }
      });
    } catch (error) {
      this.metrics.errors++;
      logger.error('Cache set error', {
        module: MODULE_NAME,
        method: 'set',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error',
        details: { namespace: this.namespace, key }
      });
    }
  }

  /**
   * 删除缓存数据
   */
  async delete(key: string): Promise<void> {
    const fullKey = this.getFullKey(key);

    try {
      // 1. 删除本地缓存
      this.localCache.delete(key);

      // 2. 删除 Redis 缓存
      await redis.del(fullKey);

      logger.debug('Cache delete', {
        module: MODULE_NAME,
        method: 'delete',
        timestamp: getTimestamp(),
        details: { namespace: this.namespace, key }
      });
    } catch (error) {
      this.metrics.errors++;
      logger.error('Cache delete error', {
        module: MODULE_NAME,
        method: 'delete',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error',
        details: { namespace: this.namespace, key }
      });
    }
  }

  /**
   * 清空缓存
   */
  async clear(): Promise<void> {
    try {
      // 1. 清空本地缓存
      this.localCache.clear();

      // 2. 清空 Redis 缓存
      const pattern = this.getFullKey('*');
      const keys = await redis.keys(pattern);
      if (keys.length > 0) {
        await redis.del(...keys);
      }

      logger.info('Cache cleared', {
        module: MODULE_NAME,
        method: 'clear',
        timestamp: getTimestamp(),
        details: { namespace: this.namespace, deletedKeys: keys.length }
      });
    } catch (error) {
      this.metrics.errors++;
      logger.error('Cache clear error', {
        module: MODULE_NAME,
        method: 'clear',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error',
        details: { namespace: this.namespace }
      });
    }
  }

  /**
   * 获取或设置缓存（如果不存在则调用工厂函数）
   */
  async getOrSet(key: string, factory: () => Promise<T>, customTTL?: number): Promise<T | null> {
    const cached = await this.get(key);
    if (cached !== null) {
      return cached;
    }

    try {
      const data = await factory();
      await this.set(key, data, customTTL);
      return data;
    } catch (error) {
      logger.error('Cache getOrSet factory error', {
        module: MODULE_NAME,
        method: 'getOrSet',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error',
        details: { namespace: this.namespace, key }
      });
      return null;
    }
  }

  /**
   * 获取缓存指标
   * @returns 包含命中率、错误率等详细缓存性能指标
   */
  getMetrics(): CacheMetrics {
    const totalHits = this.metrics.localHits + this.metrics.redisHits;
    const hitRate = this.metrics.totalRequests > 0
      ? (totalHits / this.metrics.totalRequests) * 100
      : 0;

    return {
      ...this.metrics,
      localCacheSize: this.localCache.size,
      hitRate: Math.round(hitRate * 100) / 100
    };
  }

  /**
   * 重置指标
   */
  resetMetrics(): void {
    this.metrics = {
      localHits: 0,
      localMisses: 0,
      redisHits: 0,
      redisMisses: 0,
      evictions: 0,
      errors: 0,
      totalRequests: 0
    };
  }

  /**
   * 销毁缓存实例
   */
  destroy(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
      this.syncTimer = null;
    }
    this.localCache.clear();
  }

  // 私有方法

  private getFullKey(key: string): string {
    return `unibabble:cache:${this.namespace}:${key}`;
  }

  private setLocal(key: string, data: T, customTTL?: number): void {
    const now = Date.now();
    const ttl = customTTL || this.config.localTTL;

    // 检查是否需要清理本地缓存
    if (this.localCache.size >= this.config.maxLocalSize) {
      this.evictLRU();
    }

    this.localCache.set(key, {
      data,
      timestamp: now,
      ttl,
      accessCount: 1,
      lastAccess: now
    });
  }

  private isLocalItemValid(item: CacheItem<T>): boolean {
    return Date.now() - item.timestamp < item.ttl;
  }

  private evictLRU(): void {
    let oldestKey = '';
    let oldestTime = Date.now();

    for (const [key, item] of this.localCache) {
      if (item.lastAccess < oldestTime) {
        oldestTime = item.lastAccess;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.localCache.delete(oldestKey);
      this.metrics.evictions++;
    }
  }

  private startSyncTimer(): void {
    if (this.config.syncInterval > 0) {
      this.syncTimer = setInterval(() => {
        this.cleanupExpiredLocal();
      }, this.config.syncInterval);
    }
  }

  private cleanupExpiredLocal(): void {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [key, item] of this.localCache) {
      if (!this.isLocalItemValid(item)) {
        this.localCache.delete(key);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      logger.debug('Local cache cleanup', {
        module: MODULE_NAME,
        method: 'cleanupExpiredLocal',
        timestamp: getTimestamp(),
        details: { namespace: this.namespace, cleanedCount }
      });
    }
  }
}

/**
 * 缓存管理器
 */
class CacheManager {
  private caches = new Map<string, HybridCache<any>>();

  /**
   * 获取或创建缓存实例
   */
  getCache<T>(namespace: string, config?: Partial<CacheConfig>): HybridCache<T> {
    if (!this.caches.has(namespace)) {
      this.caches.set(namespace, new HybridCache<T>(namespace, config));
    }
    return this.caches.get(namespace)!;
  }

  /**
   * 销毁所有缓存
   */
  destroyAll(): void {
    for (const cache of this.caches.values()) {
      cache.destroy();
    }
    this.caches.clear();
  }

  /**
   * 获取所有缓存的指标
   */
  getAllMetrics(): Record<string, any> {
    const metrics: Record<string, any> = {};
    for (const [namespace, cache] of this.caches) {
      metrics[namespace] = cache.getMetrics();
    }
    return metrics;
  }
}

// 全局缓存管理器实例
export const cacheManager = new CacheManager();

// 便捷函数
export function getCache<T>(namespace: string, config?: Partial<CacheConfig>): HybridCache<T> {
  return cacheManager.getCache<T>(namespace, config);
}

export default HybridCache;
