import { redis } from '@/utils/redis/client.js';
import { logger, getTimestamp } from '@unibabble/shared';

const MODULE_NAME = 'Server:utils:distributedLock';

/**
 * 分布式锁实现
 * 使用 Redis 实现分布式锁，确保在多实例环境下只有一个实例执行特定任务
 */
export class DistributedLock {
  private lockKey: string;
  private lockValue: string;
  private ttl: number;
  private retryDelay: number;
  private maxRetries: number;

  constructor(
    lockName: string,
    ttl: number = 30000, // 30秒默认过期时间
    retryDelay: number = 1000, // 1秒重试间隔
    maxRetries: number = 3 // 最大重试次数
  ) {
    this.lockKey = `unibabble:lock:${lockName}`;
    this.lockValue = `${process.pid}-${Date.now()}-${Math.random()}`;
    this.ttl = ttl;
    this.retryDelay = retryDelay;
    this.maxRetries = maxRetries;
  }

  /**
   * 获取锁
   */
  async acquire(): Promise<boolean> {
    let retries = 0;
    
    while (retries <= this.maxRetries) {
      try {
        // 使用 SET NX EX 命令原子性地设置锁
        const result = await redis.set(
          this.lockKey,
          this.lockValue,
          'PX', // 毫秒级过期时间
          this.ttl,
          'NX' // 只在键不存在时设置
        );

        if (result === 'OK') {
          logger.debug('Distributed lock acquired', {
            module: MODULE_NAME,
            method: 'acquire',
            timestamp: getTimestamp(),
            details: {
              lockKey: this.lockKey,
              lockValue: this.lockValue,
              ttl: this.ttl
            }
          });
          return true;
        }

        // 锁已被其他实例持有
        if (retries < this.maxRetries) {
          logger.debug('Lock acquisition failed, retrying', {
            module: MODULE_NAME,
            method: 'acquire',
            timestamp: getTimestamp(),
            details: {
              lockKey: this.lockKey,
              retries,
              maxRetries: this.maxRetries
            }
          });
          
          await this.sleep(this.retryDelay);
          retries++;
        } else {
          logger.warn('Lock acquisition failed after max retries', {
            module: MODULE_NAME,
            method: 'acquire',
            timestamp: getTimestamp(),
            details: {
              lockKey: this.lockKey,
              maxRetries: this.maxRetries
            }
          });
          return false;
        }
      } catch (error) {
        logger.error('Error acquiring distributed lock', {
          module: MODULE_NAME,
          method: 'acquire',
          timestamp: getTimestamp(),
          error: error instanceof Error ? error.message : 'Unknown error',
          details: {
            lockKey: this.lockKey,
            retries
          }
        });
        return false;
      }
    }

    return false;
  }

  /**
   * 释放锁
   */
  async release(): Promise<boolean> {
    try {
      // 使用 Lua 脚本确保只有锁的持有者才能释放锁
      const luaScript = `
        if redis.call("get", KEYS[1]) == ARGV[1] then
          return redis.call("del", KEYS[1])
        else
          return 0
        end
      `;

      const result = await redis.eval(luaScript, 1, this.lockKey, this.lockValue);

      if (result === 1) {
        logger.debug('Distributed lock released', {
          module: MODULE_NAME,
          method: 'release',
          timestamp: getTimestamp(),
          details: {
            lockKey: this.lockKey,
            lockValue: this.lockValue
          }
        });
        return true;
      } else {
        logger.warn('Failed to release lock - not owner or already expired', {
          module: MODULE_NAME,
          method: 'release',
          timestamp: getTimestamp(),
          details: {
            lockKey: this.lockKey,
            lockValue: this.lockValue
          }
        });
        return false;
      }
    } catch (error) {
      logger.error('Error releasing distributed lock', {
        module: MODULE_NAME,
        method: 'release',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error',
        details: {
          lockKey: this.lockKey
        }
      });
      return false;
    }
  }

  /**
   * 续期锁
   */
  async renew(): Promise<boolean> {
    try {
      // 使用 Lua 脚本确保只有锁的持有者才能续期
      const luaScript = `
        if redis.call("get", KEYS[1]) == ARGV[1] then
          return redis.call("pexpire", KEYS[1], ARGV[2])
        else
          return 0
        end
      `;

      const result = await redis.eval(luaScript, 1, this.lockKey, this.lockValue, this.ttl);

      if (result === 1) {
        logger.debug('Distributed lock renewed', {
          module: MODULE_NAME,
          method: 'renew',
          timestamp: getTimestamp(),
          details: {
            lockKey: this.lockKey,
            ttl: this.ttl
          }
        });
        return true;
      } else {
        logger.warn('Failed to renew lock - not owner or already expired', {
          module: MODULE_NAME,
          method: 'renew',
          timestamp: getTimestamp(),
          details: {
            lockKey: this.lockKey
          }
        });
        return false;
      }
    } catch (error) {
      logger.error('Error renewing distributed lock', {
        module: MODULE_NAME,
        method: 'renew',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error',
        details: {
          lockKey: this.lockKey
        }
      });
      return false;
    }
  }

  /**
   * 检查锁是否存在
   */
  async exists(): Promise<boolean> {
    try {
      const result = await redis.exists(this.lockKey);
      return result === 1;
    } catch (error) {
      logger.error('Error checking lock existence', {
        module: MODULE_NAME,
        method: 'exists',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error',
        details: {
          lockKey: this.lockKey
        }
      });
      return false;
    }
  }

  /**
   * 获取锁的剩余 TTL
   */
  async getTTL(): Promise<number> {
    try {
      return await redis.pttl(this.lockKey);
    } catch (error) {
      logger.error('Error getting lock TTL', {
        module: MODULE_NAME,
        method: 'getTTL',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error',
        details: {
          lockKey: this.lockKey
        }
      });
      return -1;
    }
  }

  /**
   * 执行带锁的任务
   */
  async executeWithLock<T>(task: () => Promise<T>): Promise<T | null> {
    const acquired = await this.acquire();
    if (!acquired) {
      logger.warn('Could not acquire lock for task execution', {
        module: MODULE_NAME,
        method: 'executeWithLock',
        timestamp: getTimestamp(),
        details: {
          lockKey: this.lockKey
        }
      });
      return null;
    }

    try {
      const result = await task();
      return result;
    } catch (error) {
      logger.error('Error executing task with lock', {
        module: MODULE_NAME,
        method: 'executeWithLock',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error',
        details: {
          lockKey: this.lockKey
        }
      });
      throw error;
    } finally {
      await this.release();
    }
  }

  /**
   * 睡眠函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * 创建分布式锁实例
 */
export function createDistributedLock(
  lockName: string,
  ttl?: number,
  retryDelay?: number,
  maxRetries?: number
): DistributedLock {
  return new DistributedLock(lockName, ttl, retryDelay, maxRetries);
}

/**
 * 便捷函数：执行带锁的任务
 */
export async function executeWithDistributedLock<T>(
  lockName: string,
  task: () => Promise<T>,
  ttl: number = 30000
): Promise<T | null> {
  const lock = createDistributedLock(lockName, ttl);
  return await lock.executeWithLock(task);
}

export default DistributedLock;
