import { logger, getTimestamp } from '@unibabble/shared';
import { isRedisHealthy } from './redis/client.js';
import type { FallbackStrategy, FallbackMetrics } from '@/types/health.js';

const MODULE_NAME = 'Server:utils:fallbackManager';

/**
 * 降级管理器
 * 当 Redis 不可用时提供备用存储方案
 */
export class FallbackManager {
  private currentStrategy: FallbackStrategy = FallbackStrategy.WRITE_THROUGH;
  private memoryStore = new Map<string, any>();
  private isRedisAvailable = true;
  private lastRedisCheck = 0;
  private checkInterval = 5000; // 5秒检查一次
  private maxMemorySize = 10000; // 最大内存条目数
  private fallbackMetrics: Omit<FallbackMetrics, 'currentStrategy' | 'isRedisAvailable' | 'memoryStoreSize' | 'maxMemorySize'> = {
    memoryHits: 0,
    memoryMisses: 0,
    redisFailures: 0,
    fallbackActivations: 0,
    lastFallbackAt: 0
  };

  constructor() {
    this.startRedisMonitoring();
  }

  /**
   * 启动 Redis 监控
   */
  private startRedisMonitoring(): void {
    setInterval(async () => {
      await this.checkRedisHealth();
    }, this.checkInterval);
  }

  /**
   * 检查 Redis 健康状态
   */
  private async checkRedisHealth(): Promise<void> {
    try {
      const now = Date.now();
      if (now - this.lastRedisCheck < this.checkInterval) {
        return;
      }

      this.lastRedisCheck = now;
      const isHealthy = await isRedisHealthy();

      if (this.isRedisAvailable && !isHealthy) {
        // Redis 从可用变为不可用
        this.isRedisAvailable = false;
        this.activateFallback();
      } else if (!this.isRedisAvailable && isHealthy) {
        // Redis 从不可用变为可用
        this.isRedisAvailable = true;
        this.deactivateFallback();
      }
    } catch (error) {
      logger.error('Redis health check failed in fallback manager', {
        module: MODULE_NAME,
        method: 'checkRedisHealth',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * 激活降级策略
   */
  private activateFallback(): void {
    this.currentStrategy = FallbackStrategy.MEMORY_ONLY;
    this.fallbackMetrics.fallbackActivations++;
    this.fallbackMetrics.lastFallbackAt = Date.now();

    logger.warn('Redis fallback activated - switching to memory-only mode', {
      module: MODULE_NAME,
      method: 'activateFallback',
      timestamp: getTimestamp(),
      details: {
        strategy: this.currentStrategy,
        memoryStoreSize: this.memoryStore.size,
        metrics: this.fallbackMetrics
      }
    });
  }

  /**
   * 停用降级策略
   */
  private deactivateFallback(): void {
    const previousStrategy = this.currentStrategy;
    this.currentStrategy = FallbackStrategy.WRITE_THROUGH;

    logger.info('Redis fallback deactivated - switching back to normal mode', {
      module: MODULE_NAME,
      method: 'deactivateFallback',
      timestamp: getTimestamp(),
      details: {
        previousStrategy,
        currentStrategy: this.currentStrategy,
        memoryStoreSize: this.memoryStore.size
      }
    });

    // 可选：清理内存存储以释放内存
    if (this.memoryStore.size > this.maxMemorySize / 2) {
      this.cleanupMemoryStore();
    }
  }

  /**
   * 获取数据（带降级支持）
   */
  async get<T>(key: string, redisGetter: () => Promise<T | null>): Promise<T | null> {
    try {
      // 如果 Redis 可用，优先从 Redis 获取
      if (this.isRedisAvailable && this.currentStrategy !== FallbackStrategy.MEMORY_ONLY) {
        try {
          const result = await redisGetter();
          if (result !== null) {
            // 同时更新内存缓存
            this.memoryStore.set(key, result);
            return result;
          }
        } catch (error) {
          this.fallbackMetrics.redisFailures++;
          logger.warn('Redis get operation failed, falling back to memory', {
            module: MODULE_NAME,
            method: 'get',
            timestamp: getTimestamp(),
            error: error instanceof Error ? error.message : 'Unknown error',
            details: { key }
          });
        }
      }

      // 从内存获取
      if (this.memoryStore.has(key)) {
        this.fallbackMetrics.memoryHits++;
        return this.memoryStore.get(key);
      }

      this.fallbackMetrics.memoryMisses++;
      return null;
    } catch (error) {
      logger.error('Fallback get operation failed', {
        module: MODULE_NAME,
        method: 'get',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error',
        details: { key }
      });
      return null;
    }
  }

  /**
   * 设置数据（带降级支持）
   */
  async set<T>(key: string, value: T, redisSetter: () => Promise<void>): Promise<void> {
    try {
      // 总是更新内存存储
      this.memoryStore.set(key, value);
      this.cleanupMemoryStoreIfNeeded();

      // 如果 Redis 可用且策略允许，同时更新 Redis
      if (this.isRedisAvailable && this.currentStrategy !== FallbackStrategy.MEMORY_ONLY) {
        try {
          await redisSetter();
        } catch (error) {
          this.fallbackMetrics.redisFailures++;
          logger.warn('Redis set operation failed, data saved to memory only', {
            module: MODULE_NAME,
            method: 'set',
            timestamp: getTimestamp(),
            error: error instanceof Error ? error.message : 'Unknown error',
            details: { key }
          });
        }
      }
    } catch (error) {
      logger.error('Fallback set operation failed', {
        module: MODULE_NAME,
        method: 'set',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error',
        details: { key }
      });
    }
  }

  /**
   * 删除数据（带降级支持）
   */
  async delete(key: string, redisDeleter: () => Promise<void>): Promise<void> {
    try {
      // 从内存删除
      this.memoryStore.delete(key);

      // 如果 Redis 可用，同时从 Redis 删除
      if (this.isRedisAvailable && this.currentStrategy !== FallbackStrategy.MEMORY_ONLY) {
        try {
          await redisDeleter();
        } catch (error) {
          this.fallbackMetrics.redisFailures++;
          logger.warn('Redis delete operation failed, removed from memory only', {
            module: MODULE_NAME,
            method: 'delete',
            timestamp: getTimestamp(),
            error: error instanceof Error ? error.message : 'Unknown error',
            details: { key }
          });
        }
      }
    } catch (error) {
      logger.error('Fallback delete operation failed', {
        module: MODULE_NAME,
        method: 'delete',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error',
        details: { key }
      });
    }
  }

  /**
   * 检查是否应该使用降级模式
   */
  shouldUseFallback(): boolean {
    return !this.isRedisAvailable || this.currentStrategy === FallbackStrategy.MEMORY_ONLY;
  }

  /**
   * 获取当前策略
   */
  getCurrentStrategy(): FallbackStrategy {
    return this.currentStrategy;
  }

  /**
   * 获取降级指标
   * @returns {FallbackMetrics} 包含内存命中率、Redis 失败次数、当前策略等的完整降级指标
   */
  getMetrics(): FallbackMetrics {
    return {
      ...this.fallbackMetrics,
      currentStrategy: this.currentStrategy,
      isRedisAvailable: this.isRedisAvailable,
      memoryStoreSize: this.memoryStore.size,
      maxMemorySize: this.maxMemorySize
    };
  }

  /**
   * 清理内存存储
   */
  private cleanupMemoryStore(): void {
    const targetSize = Math.floor(this.maxMemorySize * 0.7);
    const entries = Array.from(this.memoryStore.entries());

    // 简单的 LRU 清理：删除前面的条目
    const toDelete = entries.slice(0, entries.length - targetSize);

    for (const [key] of toDelete) {
      this.memoryStore.delete(key);
    }

    logger.info('Memory store cleanup completed', {
      module: MODULE_NAME,
      method: 'cleanupMemoryStore',
      timestamp: getTimestamp(),
      details: {
        deletedEntries: toDelete.length,
        remainingEntries: this.memoryStore.size
      }
    });
  }

  /**
   * 检查是否需要清理内存存储
   */
  private cleanupMemoryStoreIfNeeded(): void {
    if (this.memoryStore.size > this.maxMemorySize) {
      this.cleanupMemoryStore();
    }
  }

  /**
   * 强制设置策略（用于测试或手动控制）
   */
  setStrategy(strategy: FallbackStrategy): void {
    const previousStrategy = this.currentStrategy;
    this.currentStrategy = strategy;

    logger.info('Fallback strategy manually changed', {
      module: MODULE_NAME,
      method: 'setStrategy',
      timestamp: getTimestamp(),
      details: { previousStrategy, newStrategy: strategy }
    });
  }

  /**
   * 清空内存存储
   */
  clearMemoryStore(): void {
    const size = this.memoryStore.size;
    this.memoryStore.clear();

    logger.info('Memory store cleared', {
      module: MODULE_NAME,
      method: 'clearMemoryStore',
      timestamp: getTimestamp(),
      details: { clearedEntries: size }
    });
  }

  /**
   * 销毁降级管理器
   */
  destroy(): void {
    this.memoryStore.clear();
    logger.info('Fallback manager destroyed', {
      module: MODULE_NAME,
      method: 'destroy',
      timestamp: getTimestamp()
    });
  }
}

// 全局降级管理器实例
export const fallbackManager = new FallbackManager();

export default FallbackManager;
