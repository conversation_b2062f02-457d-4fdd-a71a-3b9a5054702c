import { logger, getTimestamp } from '@unibabble/shared';
import { cacheManager } from './hybridCache.js';
import { roomService, userService, statsService } from '@/services/redis/index.js';

const MODULE_NAME = 'Server:utils:cacheOptimizer';

/**
 * 缓存优化器
 * 提供缓存预热、性能监控和自动优化功能
 */
export class CacheOptimizer {
  private monitoringInterval: NodeJS.Timeout | null = null;
  private optimizationInterval: NodeJS.Timeout | null = null;

  /**
   * 启动缓存优化器
   */
  start(): void {
    this.startMonitoring();
    this.startOptimization();
    
    logger.info('Cache optimizer started', {
      module: MODULE_NAME,
      method: 'start',
      timestamp: getTimestamp()
    });
  }

  /**
   * 停止缓存优化器
   */
  stop(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    if (this.optimizationInterval) {
      clearInterval(this.optimizationInterval);
      this.optimizationInterval = null;
    }

    logger.info('Cache optimizer stopped', {
      module: MODULE_NAME,
      method: 'stop',
      timestamp: getTimestamp()
    });
  }

  /**
   * 预热房间缓存
   */
  async warmupRoomCache(): Promise<number> {
    try {
      logger.info('Starting room cache warmup', {
        module: MODULE_NAME,
        method: 'warmupRoomCache',
        timestamp: getTimestamp()
      });

      const roomIds = await roomService.getAllRoomIds();
      const roomCache = cacheManager.getCache('rooms');
      let warmedCount = 0;

      // 批量预热房间数据
      const batchSize = 50;
      for (let i = 0; i < roomIds.length; i += batchSize) {
        const batch = roomIds.slice(i, i + batchSize);
        
        await Promise.all(batch.map(async (roomId) => {
          try {
            const room = await roomService.getRoom(roomId);
            if (room) {
              await roomCache.set(roomId.toString(), room);
              warmedCount++;
            }
          } catch (error) {
            logger.warn('Failed to warmup room cache', {
              module: MODULE_NAME,
              method: 'warmupRoomCache',
              timestamp: getTimestamp(),
              error: error instanceof Error ? error.message : 'Unknown error',
              details: { roomId }
            });
          }
        }));

        // 避免过度负载，批次间稍作延迟
        if (i + batchSize < roomIds.length) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      logger.info('Room cache warmup completed', {
        module: MODULE_NAME,
        method: 'warmupRoomCache',
        timestamp: getTimestamp(),
        details: { totalRooms: roomIds.length, warmedCount }
      });

      return warmedCount;
    } catch (error) {
      logger.error('Room cache warmup failed', {
        module: MODULE_NAME,
        method: 'warmupRoomCache',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return 0;
    }
  }

  /**
   * 预热用户缓存
   */
  async warmupUserCache(): Promise<number> {
    try {
      logger.info('Starting user cache warmup', {
        module: MODULE_NAME,
        method: 'warmupUserCache',
        timestamp: getTimestamp()
      });

      const userIds = await userService.getAllUserIds();
      const userCache = cacheManager.getCache('users');
      let warmedCount = 0;

      // 只预热最近活跃的用户（限制数量避免内存过载）
      const maxUsers = Math.min(userIds.length, 500);
      const recentUserIds = userIds.slice(0, maxUsers);

      const batchSize = 30;
      for (let i = 0; i < recentUserIds.length; i += batchSize) {
        const batch = recentUserIds.slice(i, i + batchSize);
        
        await Promise.all(batch.map(async (userId) => {
          try {
            const user = await userService.getUserWithStatus(userId);
            if (user) {
              await userCache.set(userId, user);
              warmedCount++;
            }
          } catch (error) {
            logger.warn('Failed to warmup user cache', {
              module: MODULE_NAME,
              method: 'warmupUserCache',
              timestamp: getTimestamp(),
              error: error instanceof Error ? error.message : 'Unknown error',
              details: { userId }
            });
          }
        }));

        if (i + batchSize < recentUserIds.length) {
          await new Promise(resolve => setTimeout(resolve, 150));
        }
      }

      logger.info('User cache warmup completed', {
        module: MODULE_NAME,
        method: 'warmupUserCache',
        timestamp: getTimestamp(),
        details: { totalUsers: userIds.length, processedUsers: recentUserIds.length, warmedCount }
      });

      return warmedCount;
    } catch (error) {
      logger.error('User cache warmup failed', {
        module: MODULE_NAME,
        method: 'warmupUserCache',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return 0;
    }
  }

  /**
   * 分析缓存性能
   */
  analyzeCachePerformance(): {
    summary: any;
    recommendations: string[];
    issues: string[];
  } {
    const metrics = cacheManager.getAllMetrics();
    const recommendations: string[] = [];
    const issues: string[] = [];
    
    let totalRequests = 0;
    let totalHits = 0;
    let totalErrors = 0;

    for (const [namespace, metric] of Object.entries(metrics)) {
      totalRequests += metric.totalRequests;
      totalHits += metric.localHits + metric.redisHits;
      totalErrors += metric.errors;

      // 分析各个缓存的性能
      if (metric.hitRate < 60 && metric.totalRequests > 50) {
        issues.push(`Low hit rate (${metric.hitRate}%) for ${namespace} cache`);
        recommendations.push(`Consider increasing TTL or reviewing access patterns for ${namespace}`);
      }

      if (metric.errors > 0) {
        const errorRate = (metric.errors / metric.totalRequests) * 100;
        if (errorRate > 2) {
          issues.push(`High error rate (${errorRate.toFixed(2)}%) for ${namespace} cache`);
          recommendations.push(`Investigate and fix errors in ${namespace} cache`);
        }
      }

      if (metric.localCacheSize > 800) {
        recommendations.push(`Consider increasing maxLocalSize for ${namespace} cache (current: ${metric.localCacheSize})`);
      }

      // 检查缓存使用效率
      const localHitRatio = metric.localHits / (metric.localHits + metric.localMisses);
      if (localHitRatio < 0.3 && metric.totalRequests > 100) {
        recommendations.push(`Local cache efficiency is low for ${namespace}, consider adjusting local TTL`);
      }
    }

    const overallHitRate = totalRequests > 0 ? (totalHits / totalRequests) * 100 : 0;
    const overallErrorRate = totalRequests > 0 ? (totalErrors / totalRequests) * 100 : 0;

    const summary = {
      totalRequests,
      totalHits,
      totalErrors,
      overallHitRate: Math.round(overallHitRate * 100) / 100,
      overallErrorRate: Math.round(overallErrorRate * 100) / 100,
      activeCaches: Object.keys(metrics).length
    };

    // 总体建议
    if (overallHitRate < 70) {
      recommendations.push('Overall cache hit rate is low, consider reviewing cache strategies');
    }

    if (overallErrorRate > 1) {
      issues.push('Overall cache error rate is high, investigate Redis connectivity');
    }

    return { summary, recommendations, issues };
  }

  /**
   * 自动优化缓存配置
   */
  async autoOptimize(): Promise<void> {
    try {
      const analysis = this.analyzeCachePerformance();
      
      logger.info('Cache performance analysis', {
        module: MODULE_NAME,
        method: 'autoOptimize',
        timestamp: getTimestamp(),
        details: {
          summary: analysis.summary,
          issuesCount: analysis.issues.length,
          recommendationsCount: analysis.recommendations.length
        }
      });

      // 如果有严重问题，记录警告
      if (analysis.issues.length > 0) {
        logger.warn('Cache performance issues detected', {
          module: MODULE_NAME,
          method: 'autoOptimize',
          timestamp: getTimestamp(),
          details: { issues: analysis.issues }
        });
      }

      // 记录优化建议
      if (analysis.recommendations.length > 0) {
        logger.info('Cache optimization recommendations', {
          module: MODULE_NAME,
          method: 'autoOptimize',
          timestamp: getTimestamp(),
          details: { recommendations: analysis.recommendations }
        });
      }

      // 执行自动清理
      await this.performMaintenance();

    } catch (error) {
      logger.error('Auto optimization failed', {
        module: MODULE_NAME,
        method: 'autoOptimize',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * 执行缓存维护
   */
  async performMaintenance(): Promise<void> {
    try {
      const metrics = cacheManager.getAllMetrics();
      let cleanedCaches = 0;

      for (const [namespace, metric] of Object.entries(metrics)) {
        // 如果缓存错误率过高，清空该缓存
        const errorRate = metric.totalRequests > 0 ? (metric.errors / metric.totalRequests) * 100 : 0;
        if (errorRate > 10 && metric.totalRequests > 20) {
          const cache = cacheManager.getCache(namespace);
          await cache.clear();
          cache.resetMetrics();
          cleanedCaches++;
          
          logger.warn('Cache cleared due to high error rate', {
            module: MODULE_NAME,
            method: 'performMaintenance',
            timestamp: getTimestamp(),
            details: { namespace, errorRate }
          });
        }
      }

      if (cleanedCaches > 0) {
        logger.info('Cache maintenance completed', {
          module: MODULE_NAME,
          method: 'performMaintenance',
          timestamp: getTimestamp(),
          details: { cleanedCaches }
        });
      }
    } catch (error) {
      logger.error('Cache maintenance failed', {
        module: MODULE_NAME,
        method: 'performMaintenance',
        timestamp: getTimestamp(),
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  // 私有方法

  private startMonitoring(): void {
    // 每5分钟监控一次缓存性能
    this.monitoringInterval = setInterval(() => {
      const analysis = this.analyzeCachePerformance();
      
      if (analysis.issues.length > 0 || analysis.summary.overallHitRate < 50) {
        logger.warn('Cache performance monitoring alert', {
          module: MODULE_NAME,
          method: 'monitoring',
          timestamp: getTimestamp(),
          details: analysis
        });
      }
    }, 5 * 60 * 1000);
  }

  private startOptimization(): void {
    // 每30分钟执行一次自动优化
    this.optimizationInterval = setInterval(() => {
      this.autoOptimize();
    }, 30 * 60 * 1000);
  }
}

// 全局缓存优化器实例
export const cacheOptimizer = new CacheOptimizer();

export default CacheOptimizer;
