import { Router } from 'express';
import { cacheManager } from '@/utils/hybridCache.js';
import { logger, getTimestamp } from '@unibabble/shared';

const MODULE_NAME = 'Server:routes:cache';
const router = Router();

/**
 * 获取所有缓存的指标
 */
router.get('/metrics', async (req, res) => {
  try {
    const metrics = cacheManager.getAllMetrics();
    
    // 计算总体统计
    const totalStats = {
      totalCaches: Object.keys(metrics).length,
      totalLocalHits: 0,
      totalLocalMisses: 0,
      totalRedisHits: 0,
      totalRedisMisses: 0,
      totalRequests: 0,
      totalErrors: 0,
      averageHitRate: 0
    };

    let totalHitRate = 0;
    for (const [namespace, metric] of Object.entries(metrics)) {
      totalStats.totalLocalHits += metric.localHits;
      totalStats.totalLocalMisses += metric.localMisses;
      totalStats.totalRedisHits += metric.redisHits;
      totalStats.totalRedisMisses += metric.redisMisses;
      totalStats.totalRequests += metric.totalRequests;
      totalStats.totalErrors += metric.errors;
      totalHitRate += metric.hitRate;
    }

    if (totalStats.totalCaches > 0) {
      totalStats.averageHitRate = Math.round((totalHitRate / totalStats.totalCaches) * 100) / 100;
    }

    res.json({
      success: true,
      data: {
        summary: totalStats,
        details: metrics,
        timestamp: getTimestamp()
      }
    });

    logger.debug('Cache metrics retrieved', {
      module: MODULE_NAME,
      method: 'getMetrics',
      timestamp: getTimestamp(),
      details: { totalCaches: totalStats.totalCaches }
    });
  } catch (error) {
    logger.error('Failed to get cache metrics', {
      module: MODULE_NAME,
      method: 'getMetrics',
      timestamp: getTimestamp(),
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    res.status(500).json({
      success: false,
      error: 'Failed to get cache metrics'
    });
  }
});

/**
 * 清空指定缓存
 */
router.delete('/:namespace', async (req, res) => {
  try {
    const { namespace } = req.params;
    
    if (!namespace) {
      return res.status(400).json({
        success: false,
        error: 'Namespace is required'
      });
    }

    const cache = cacheManager.getCache(namespace);
    await cache.clear();

    logger.info('Cache cleared', {
      module: MODULE_NAME,
      method: 'clearCache',
      timestamp: getTimestamp(),
      details: { namespace }
    });

    res.json({
      success: true,
      message: `Cache '${namespace}' cleared successfully`
    });
  } catch (error) {
    logger.error('Failed to clear cache', {
      module: MODULE_NAME,
      method: 'clearCache',
      timestamp: getTimestamp(),
      error: error instanceof Error ? error.message : 'Unknown error',
      details: { namespace: req.params.namespace }
    });

    res.status(500).json({
      success: false,
      error: 'Failed to clear cache'
    });
  }
});

/**
 * 清空所有缓存
 */
router.delete('/', async (req, res) => {
  try {
    const metrics = cacheManager.getAllMetrics();
    const namespaces = Object.keys(metrics);

    // 清空所有缓存
    for (const namespace of namespaces) {
      const cache = cacheManager.getCache(namespace);
      await cache.clear();
    }

    logger.info('All caches cleared', {
      module: MODULE_NAME,
      method: 'clearAllCaches',
      timestamp: getTimestamp(),
      details: { clearedCaches: namespaces.length, namespaces }
    });

    res.json({
      success: true,
      message: `All ${namespaces.length} caches cleared successfully`,
      data: { clearedNamespaces: namespaces }
    });
  } catch (error) {
    logger.error('Failed to clear all caches', {
      module: MODULE_NAME,
      method: 'clearAllCaches',
      timestamp: getTimestamp(),
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    res.status(500).json({
      success: false,
      error: 'Failed to clear all caches'
    });
  }
});

/**
 * 重置指定缓存的指标
 */
router.post('/:namespace/reset-metrics', async (req, res) => {
  try {
    const { namespace } = req.params;
    
    if (!namespace) {
      return res.status(400).json({
        success: false,
        error: 'Namespace is required'
      });
    }

    const cache = cacheManager.getCache(namespace);
    cache.resetMetrics();

    logger.info('Cache metrics reset', {
      module: MODULE_NAME,
      method: 'resetMetrics',
      timestamp: getTimestamp(),
      details: { namespace }
    });

    res.json({
      success: true,
      message: `Metrics for cache '${namespace}' reset successfully`
    });
  } catch (error) {
    logger.error('Failed to reset cache metrics', {
      module: MODULE_NAME,
      method: 'resetMetrics',
      timestamp: getTimestamp(),
      error: error instanceof Error ? error.message : 'Unknown error',
      details: { namespace: req.params.namespace }
    });

    res.status(500).json({
      success: false,
      error: 'Failed to reset cache metrics'
    });
  }
});

/**
 * 获取缓存健康状态
 */
router.get('/health', async (req, res) => {
  try {
    const metrics = cacheManager.getAllMetrics();
    const health = {
      status: 'healthy',
      issues: [] as string[],
      recommendations: [] as string[],
      caches: {} as Record<string, any>
    };

    for (const [namespace, metric] of Object.entries(metrics)) {
      const cacheHealth = {
        status: 'healthy',
        hitRate: metric.hitRate,
        errorRate: metric.totalRequests > 0 ? (metric.errors / metric.totalRequests) * 100 : 0,
        localCacheSize: metric.localCacheSize
      };

      // 检查命中率
      if (metric.hitRate < 50 && metric.totalRequests > 100) {
        cacheHealth.status = 'warning';
        health.issues.push(`Low hit rate (${metric.hitRate}%) for cache '${namespace}'`);
        health.recommendations.push(`Consider increasing TTL or reviewing cache strategy for '${namespace}'`);
      }

      // 检查错误率
      if (cacheHealth.errorRate > 5) {
        cacheHealth.status = 'error';
        health.issues.push(`High error rate (${cacheHealth.errorRate.toFixed(2)}%) for cache '${namespace}'`);
        health.recommendations.push(`Investigate errors in cache '${namespace}'`);
      }

      // 检查本地缓存大小
      if (metric.localCacheSize > 800) {
        health.recommendations.push(`Local cache size is high (${metric.localCacheSize}) for '${namespace}', consider increasing maxLocalSize`);
      }

      health.caches[namespace] = cacheHealth;
    }

    // 设置总体健康状态
    if (health.issues.length > 0) {
      health.status = health.issues.some(issue => issue.includes('error')) ? 'error' : 'warning';
    }

    res.json({
      success: true,
      data: health,
      timestamp: getTimestamp()
    });

    logger.debug('Cache health check completed', {
      module: MODULE_NAME,
      method: 'healthCheck',
      timestamp: getTimestamp(),
      details: { 
        status: health.status, 
        issues: health.issues.length,
        caches: Object.keys(health.caches).length
      }
    });
  } catch (error) {
    logger.error('Failed to check cache health', {
      module: MODULE_NAME,
      method: 'healthCheck',
      timestamp: getTimestamp(),
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    res.status(500).json({
      success: false,
      error: 'Failed to check cache health'
    });
  }
});

/**
 * 预热缓存（可选功能）
 */
router.post('/warmup', async (req, res) => {
  try {
    const { namespaces } = req.body;
    
    if (!Array.isArray(namespaces)) {
      return res.status(400).json({
        success: false,
        error: 'Namespaces array is required'
      });
    }

    const results = [];
    
    for (const namespace of namespaces) {
      try {
        // 这里可以根据不同的缓存类型实现预热逻辑
        // 例如预加载热门房间、活跃用户等
        results.push({
          namespace,
          status: 'success',
          message: `Cache '${namespace}' warmup completed`
        });
      } catch (error) {
        results.push({
          namespace,
          status: 'error',
          message: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    logger.info('Cache warmup completed', {
      module: MODULE_NAME,
      method: 'warmupCache',
      timestamp: getTimestamp(),
      details: { namespaces, results }
    });

    res.json({
      success: true,
      message: 'Cache warmup completed',
      data: results
    });
  } catch (error) {
    logger.error('Failed to warmup cache', {
      module: MODULE_NAME,
      method: 'warmupCache',
      timestamp: getTimestamp(),
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    res.status(500).json({
      success: false,
      error: 'Failed to warmup cache'
    });
  }
});

export default router;
