import { Router } from 'express';
import {
  isRedisHealthy,
  getRedisConnectionStatus,
  getRedisConnectionMetrics
} from '@/utils/redis/client.js';
import { fallbackManager } from '@/utils/fallbackManager.js';
import { checkRedisHealth, getRedisServiceStats } from '@/services/redis/index.js';
import { logger, getTimestamp } from '@unibabble/shared';
import type {
  RedisHealthReport,
  ServiceHealthStatus,
  RedisDiagnosticInfo
} from '@/types/redis-health.js';

const MODULE_NAME = 'Server:routes:redisHealth';
const router = Router();

/**
 * 获取 Redis 健康状态
 * @route GET /redis/health
 * @returns {RedisHealthReport} 包含连接状态、服务健康、统计信息和降级指标的完整健康报告
 */
router.get('/health', async (req, res) => {
  try {
    const [isHealthy, connectionStatus, connectionMetrics, serviceHealth, serviceStats] = await Promise.all([
      isRedisHealthy(),
      getRedisConnectionStatus(),
      getRedisConnectionMetrics(),
      checkRedisHealth(),
      getRedisServiceStats()
    ]);

    const fallbackMetrics = fallbackManager.getMetrics();

    const healthReport: RedisHealthReport = {
      overall: {
        status: isHealthy ? ServiceHealthStatus.HEALTHY : ServiceHealthStatus.UNHEALTHY,
        isRedisAvailable: isHealthy,
        connectionStatus,
        fallbackActive: fallbackManager.shouldUseFallback()
      },
      connection: connectionMetrics,
      services: serviceHealth,
      stats: serviceStats,
      fallback: fallbackMetrics,
      timestamp: getTimestamp()
    };

    // 设置适当的 HTTP 状态码
    const statusCode = isHealthy ? 200 : 503;

    res.status(statusCode).json({
      success: isHealthy,
      data: healthReport
    });

    logger.debug('Redis health check completed', {
      module: MODULE_NAME,
      method: 'health',
      timestamp: getTimestamp(),
      details: {
        isHealthy,
        connectionStatus,
        fallbackActive: fallbackManager.shouldUseFallback()
      }
    });
  } catch (error) {
    logger.error('Redis health check failed', {
      module: MODULE_NAME,
      method: 'health',
      timestamp: getTimestamp(),
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    res.status(500).json({
      success: false,
      error: 'Health check failed',
      data: {
        overall: { status: 'error' },
        timestamp: getTimestamp()
      }
    });
  }
});

/**
 * 获取 Redis 连接指标
 */
router.get('/metrics', async (req, res) => {
  try {
    const connectionMetrics = getRedisConnectionMetrics();
    const fallbackMetrics = fallbackManager.getMetrics();
    const serviceStats = await getRedisServiceStats();

    const metrics = {
      connection: connectionMetrics,
      fallback: fallbackMetrics,
      services: serviceStats,
      timestamp: getTimestamp()
    };

    res.json({
      success: true,
      data: metrics
    });

    logger.debug('Redis metrics retrieved', {
      module: MODULE_NAME,
      method: 'metrics',
      timestamp: getTimestamp()
    });
  } catch (error) {
    logger.error('Failed to get Redis metrics', {
      module: MODULE_NAME,
      method: 'metrics',
      timestamp: getTimestamp(),
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    res.status(500).json({
      success: false,
      error: 'Failed to get metrics'
    });
  }
});

/**
 * 获取降级状态
 */
router.get('/fallback', async (req, res) => {
  try {
    const fallbackMetrics = fallbackManager.getMetrics();
    const isActive = fallbackManager.shouldUseFallback();

    res.json({
      success: true,
      data: {
        isActive,
        strategy: fallbackManager.getCurrentStrategy(),
        metrics: fallbackMetrics,
        timestamp: getTimestamp()
      }
    });

    logger.debug('Fallback status retrieved', {
      module: MODULE_NAME,
      method: 'fallback',
      timestamp: getTimestamp(),
      details: { isActive, strategy: fallbackManager.getCurrentStrategy() }
    });
  } catch (error) {
    logger.error('Failed to get fallback status', {
      module: MODULE_NAME,
      method: 'fallback',
      timestamp: getTimestamp(),
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    res.status(500).json({
      success: false,
      error: 'Failed to get fallback status'
    });
  }
});

/**
 * 清空降级内存存储
 */
router.delete('/fallback/memory', async (req, res) => {
  try {
    fallbackManager.clearMemoryStore();

    logger.info('Fallback memory store cleared', {
      module: MODULE_NAME,
      method: 'clearFallbackMemory',
      timestamp: getTimestamp()
    });

    res.json({
      success: true,
      message: 'Fallback memory store cleared successfully'
    });
  } catch (error) {
    logger.error('Failed to clear fallback memory store', {
      module: MODULE_NAME,
      method: 'clearFallbackMemory',
      timestamp: getTimestamp(),
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    res.status(500).json({
      success: false,
      error: 'Failed to clear fallback memory store'
    });
  }
});

/**
 * 强制设置降级策略（仅用于测试）
 */
router.post('/fallback/strategy', async (req, res) => {
  try {
    const { strategy } = req.body;

    if (!strategy || !['memory_only', 'read_only', 'write_through', 'disabled'].includes(strategy)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid strategy. Must be one of: memory_only, read_only, write_through, disabled'
      });
    }

    fallbackManager.setStrategy(strategy);

    logger.warn('Fallback strategy manually changed', {
      module: MODULE_NAME,
      method: 'setFallbackStrategy',
      timestamp: getTimestamp(),
      details: { strategy }
    });

    res.json({
      success: true,
      message: `Fallback strategy set to ${strategy}`,
      data: {
        strategy: fallbackManager.getCurrentStrategy(),
        timestamp: getTimestamp()
      }
    });
  } catch (error) {
    logger.error('Failed to set fallback strategy', {
      module: MODULE_NAME,
      method: 'setFallbackStrategy',
      timestamp: getTimestamp(),
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    res.status(500).json({
      success: false,
      error: 'Failed to set fallback strategy'
    });
  }
});

/**
 * 获取详细的诊断信息
 */
router.get('/diagnostics', async (req, res) => {
  try {
    const [isHealthy, connectionMetrics, serviceHealth] = await Promise.all([
      isRedisHealthy(),
      getRedisConnectionMetrics(),
      checkRedisHealth()
    ]);

    const fallbackMetrics = fallbackManager.getMetrics();

    // 分析问题和建议
    const issues: string[] = [];
    const recommendations: string[] = [];

    if (!isHealthy) {
      issues.push('Redis connection is not healthy');
      recommendations.push('Check Redis server status and network connectivity');
    }

    if (connectionMetrics.totalErrors > 0) {
      issues.push(`${connectionMetrics.totalErrors} connection errors detected`);
      recommendations.push('Review Redis server logs and network stability');
    }

    if (fallbackMetrics.redisFailures > 10) {
      issues.push(`High number of Redis failures: ${fallbackMetrics.redisFailures}`);
      recommendations.push('Consider increasing Redis timeout settings or checking server capacity');
    }

    if (fallbackMetrics.memoryStoreSize > 5000) {
      issues.push(`Large fallback memory store: ${fallbackMetrics.memoryStoreSize} entries`);
      recommendations.push('Consider increasing Redis availability or reducing data retention');
    }

    const diagnostics = {
      summary: {
        status: isHealthy ? 'healthy' : 'unhealthy',
        issuesCount: issues.length,
        recommendationsCount: recommendations.length
      },
      connection: connectionMetrics,
      services: serviceHealth,
      fallback: fallbackMetrics,
      issues,
      recommendations,
      timestamp: getTimestamp()
    };

    res.json({
      success: true,
      data: diagnostics
    });

    logger.info('Redis diagnostics completed', {
      module: MODULE_NAME,
      method: 'diagnostics',
      timestamp: getTimestamp(),
      details: {
        status: diagnostics.summary.status,
        issuesCount: issues.length
      }
    });
  } catch (error) {
    logger.error('Redis diagnostics failed', {
      module: MODULE_NAME,
      method: 'diagnostics',
      timestamp: getTimestamp(),
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    res.status(500).json({
      success: false,
      error: 'Diagnostics failed'
    });
  }
});

export default router;
