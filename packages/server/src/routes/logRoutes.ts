import { Router, type Router as ExpressRouter } from 'express';
import { logManager } from '@/managers/logManager.js';
import { LogLevel } from '@/configs/logging.js';
import type { LogQueryParams, LogStatsParams } from '@/types/logging.js';
import { logger, getTimestamp } from '@unibabble/shared';

const MODULE_NAME = 'Server:routes:logs';
const logRouter: ExpressRouter = Router();

/**
 * 查询日志条目
 * GET /api/logs/query
 */
logRouter.get('/query', async (req, res) => {
  try {
    const {
      startTime,
      endTime,
      levels,
      modules,
      methods,
      userIds,
      keyword,
      page = 1,
      pageSize = 50,
      sortBy = 'timestamp',
      sortOrder = 'desc'
    } = req.query;

    const params: LogQueryParams = {
      startTime: startTime ? parseInt(startTime as string) : undefined,
      endTime: endTime ? parseInt(endTime as string) : undefined,
      levels: levels ? (levels as string).split(',') as LogLevel[] : undefined,
      modules: modules ? (modules as string).split(',') : undefined,
      methods: methods ? (methods as string).split(',') : undefined,
      userIds: userIds ? (userIds as string).split(',') : undefined,
      keyword: keyword as string,
      page: parseInt(page as string),
      pageSize: Math.min(parseInt(pageSize as string), 1000), // 限制最大页面大小
      sortBy: sortBy as 'timestamp' | 'level',
      sortOrder: sortOrder as 'asc' | 'desc'
    };

    const result = await logManager.queryLogs(params);

    res.json({
      success: true,
      data: result,
      timestamp: getTimestamp()
    });

    logger.debug('Log query completed', {
      module: MODULE_NAME,
      method: 'queryLogs',
      timestamp: getTimestamp(),
      details: { 
        params, 
        resultCount: result.entries.length,
        total: result.total 
      }
    });

  } catch (error) {
    logger.error('Failed to query logs', {
      module: MODULE_NAME,
      method: 'queryLogs',
      timestamp: getTimestamp(),
      error: error instanceof Error ? error : undefined
    });

    res.status(500).json({
      success: false,
      error: 'Failed to query logs',
      timestamp: getTimestamp()
    });
  }
});

/**
 * 获取日志统计
 * GET /api/logs/stats
 */
logRouter.get('/stats', async (req, res) => {
  try {
    const {
      startTime,
      endTime,
      groupBy = 'hour',
      levels,
      modules
    } = req.query;

    const params: LogStatsParams = {
      startTime: startTime ? parseInt(startTime as string) : undefined,
      endTime: endTime ? parseInt(endTime as string) : undefined,
      groupBy: groupBy as 'hour' | 'day' | 'level' | 'module',
      levels: levels ? (levels as string).split(',') as LogLevel[] : undefined,
      modules: modules ? (modules as string).split(',') : undefined
    };

    const stats = await logManager.getStats(params);

    res.json({
      success: true,
      data: stats,
      timestamp: getTimestamp()
    });

    logger.debug('Log stats retrieved', {
      module: MODULE_NAME,
      method: 'getStats',
      timestamp: getTimestamp(),
      details: { params, total: stats.total }
    });

  } catch (error) {
    logger.error('Failed to get log stats', {
      module: MODULE_NAME,
      method: 'getStats',
      timestamp: getTimestamp(),
      error: error instanceof Error ? error : undefined
    });

    res.status(500).json({
      success: false,
      error: 'Failed to get log stats',
      timestamp: getTimestamp()
    });
  }
});

/**
 * 获取日志健康状态
 * GET /api/logs/health
 */
logRouter.get('/health', async (req, res) => {
  try {
    const health = await logManager.getHealth();

    const statusCode = health.status === 'healthy' ? 200 : 
                      health.status === 'warning' ? 200 : 503;

    res.status(statusCode).json({
      success: health.status !== 'error',
      data: health,
      timestamp: getTimestamp()
    });

    logger.debug('Log health check completed', {
      module: MODULE_NAME,
      method: 'getHealth',
      timestamp: getTimestamp(),
      details: { 
        status: health.status,
        totalEntries: health.totalEntries,
        issues: health.issues.length
      }
    });

  } catch (error) {
    logger.error('Failed to get log health', {
      module: MODULE_NAME,
      method: 'getHealth',
      timestamp: getTimestamp(),
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    res.status(500).json({
      success: false,
      error: 'Failed to get log health',
      data: {
        status: 'error',
        issues: ['Health check failed'],
        timestamp: getTimestamp()
      }
    });
  }
});

/**
 * 清理过期日志
 * POST /api/logs/cleanup
 */
logRouter.post('/cleanup', async (req, res) => {
  try {
    const result = await logManager.cleanup();

    res.json({
      success: true,
      data: result,
      message: `Cleanup completed: ${result.deletedEntries} entries deleted`,
      timestamp: getTimestamp()
    });

    logger.info('Log cleanup completed', {
      module: MODULE_NAME,
      method: 'cleanup',
      timestamp: getTimestamp(),
      details: result
    });

  } catch (error) {
    logger.error('Failed to cleanup logs', {
      module: MODULE_NAME,
      method: 'cleanup',
      timestamp: getTimestamp(),
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    res.status(500).json({
      success: false,
      error: 'Failed to cleanup logs',
      timestamp: getTimestamp()
    });
  }
});

/**
 * 强制刷新日志缓冲区
 * POST /api/logs/flush
 */
logRouter.post('/flush', async (req, res) => {
  try {
    await logManager.forceFlush();

    res.json({
      success: true,
      message: 'Log buffers flushed successfully',
      timestamp: getTimestamp()
    });

    logger.info('Log buffers flushed', {
      module: MODULE_NAME,
      method: 'flush',
      timestamp: getTimestamp()
    });

  } catch (error) {
    logger.error('Failed to flush log buffers', {
      module: MODULE_NAME,
      method: 'flush',
      timestamp: getTimestamp(),
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    res.status(500).json({
      success: false,
      error: 'Failed to flush log buffers',
      timestamp: getTimestamp()
    });
  }
});

/**
 * 获取最近的日志条目
 * GET /api/logs/recent
 */
logRouter.get('/recent', async (req, res) => {
  try {
    const {
      count = 100,
      level,
      module
    } = req.query;

    const params: LogQueryParams = {
      startTime: Date.now() - 24 * 60 * 60 * 1000, // 最近24小时
      endTime: Date.now(),
      levels: level ? [level as LogLevel] : undefined,
      modules: module ? [module as string] : undefined,
      page: 1,
      pageSize: Math.min(parseInt(count as string), 1000),
      sortOrder: 'desc'
    };

    const result = await logManager.queryLogs(params);

    res.json({
      success: true,
      data: {
        entries: result.entries,
        total: result.total,
        count: result.entries.length
      },
      timestamp: getTimestamp()
    });

    logger.debug('Recent logs retrieved', {
      module: MODULE_NAME,
      method: 'getRecent',
      timestamp: getTimestamp(),
      details: { 
        count: result.entries.length,
        level,
        module
      }
    });

  } catch (error) {
    logger.error('Failed to get recent logs', {
      module: MODULE_NAME,
      method: 'getRecent',
      timestamp: getTimestamp(),
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    res.status(500).json({
      success: false,
      error: 'Failed to get recent logs',
      timestamp: getTimestamp()
    });
  }
});

/**
 * 获取错误日志摘要
 * GET /api/logs/errors
 */
logRouter.get('/errors', async (req, res) => {
  try {
    const {
      startTime,
      endTime,
      limit = 50
    } = req.query;

    const params: LogQueryParams = {
      startTime: startTime ? parseInt(startTime as string) : Date.now() - 24 * 60 * 60 * 1000,
      endTime: endTime ? parseInt(endTime as string) : Date.now(),
      levels: [LogLevel.ERROR],
      page: 1,
      pageSize: Math.min(parseInt(limit as string), 1000),
      sortOrder: 'desc'
    };

    const result = await logManager.queryLogs(params);

    // 分析错误模式
    const errorPatterns = new Map<string, number>();
    const errorsByModule = new Map<string, number>();

    result.entries.forEach(entry => {
      if (entry.error) {
        const pattern = entry.error.name || 'Unknown Error';
        errorPatterns.set(pattern, (errorPatterns.get(pattern) || 0) + 1);
      }

      if (entry.module) {
        errorsByModule.set(entry.module, (errorsByModule.get(entry.module) || 0) + 1);
      }
    });

    const summary = {
      totalErrors: result.total,
      recentErrors: result.entries,
      errorPatterns: Array.from(errorPatterns.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 10)
        .map(([pattern, count]) => ({ pattern, count })),
      errorsByModule: Array.from(errorsByModule.entries())
        .sort((a, b) => b[1] - a[1])
        .map(([module, count]) => ({ module, count }))
    };

    res.json({
      success: true,
      data: summary,
      timestamp: getTimestamp()
    });

    logger.debug('Error logs summary retrieved', {
      module: MODULE_NAME,
      method: 'getErrors',
      timestamp: getTimestamp(),
      details: { 
        totalErrors: result.total,
        recentCount: result.entries.length
      }
    });

  } catch (error) {
    logger.error('Failed to get error logs', {
      module: MODULE_NAME,
      method: 'getErrors',
      timestamp: getTimestamp(),
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    res.status(500).json({
      success: false,
      error: 'Failed to get error logs',
      timestamp: getTimestamp()
    });
  }
});

export default logRouter;
