{"name": "@unibabble/shared", "version": "0.0.1", "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "default": "./dist/index.js"}}, "sideEffects": false, "scripts": {"clean": "npx rimraf dist", "prebuild": "npx pnpm clean", "build": "npx tsc --build tsconfig.json", "build:verbose": "tsc --build --verbose tsconfig.json", "build:trace": "tsc --build --verbose --traceResolution tsconfig.json", "build:watch": "tsc -w", "dev": "npx pnpm build:watch", "lint": "eslint . --ext .ts", "type-check": "tsc --noEmit"}, "private": true, "dependencies": {}}